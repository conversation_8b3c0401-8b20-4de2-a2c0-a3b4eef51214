"""
Outreach Agent Service

Generates personalized cold emails and LinkedIn messages based on audit reports.
"""

import os
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import openai
from supabase import create_client, Client
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Outreach Agent Service",
    description="Generates personalized cold outreach messages based on audit reports",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Supabase client
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_ANON_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

# Initialize OpenAI client
openai.api_key = os.getenv("OPENAI_API_KEY")

# Pydantic models
class OutreachRequest(BaseModel):
    lead_id: str = Field(..., description="Lead ID to generate outreach for")
    audit_report_id: str = Field(..., description="Audit report ID to base outreach on")
    message_types: List[str] = Field(default=["email", "linkedin"], description="Types of messages to generate")
    tone: str = Field(default="professional", description="Tone of the message: professional, friendly, direct")
    include_audit_link: bool = Field(default=True, description="Whether to include link to audit report")
    generate_sequences: bool = Field(default=True, description="Whether to generate full sequences (5 emails, 3 LinkedIn)")
    sequence_spacing_days: List[int] = Field(default=[0, 3, 7, 14, 21], description="Days between sequence messages")

class OutreachResponse(BaseModel):
    success: bool
    message: str
    generated_messages: List[Dict[str, Any]]
    lead_id: str
    audit_report_id: str

class MessageGenerationRequest(BaseModel):
    lead_data: Dict[str, Any]
    audit_data: Dict[str, Any]
    message_type: str  # email or linkedin
    tone: str = "professional"
    include_audit_link: bool = True

class OutreachMessage(BaseModel):
    id: str
    lead_id: str
    audit_report_id: str
    message_type: str
    status: str
    subject_line: Optional[str]
    message_body: str
    personalization_data: Dict[str, Any]
    created_at: datetime

# Outreach generation service
class OutreachGenerator:
    def __init__(self):
        self.model = "gpt-4"

    async def generate_email_sequence(self, lead_data: Dict, audit_data: Dict, tone: str = "professional", include_audit_link: bool = True, sequence_days: List[int] = [0, 3, 7, 14, 21]) -> List[Dict[str, str]]:
        """Generate a 5-email sequence based on audit results."""

        # Define email sequence strategy based on audit performance
        grade = audit_data.get("grade", "C")
        responded = audit_data.get("responded", False)
        facebook_pixel = audit_data.get("facebook_pixel_detected", False)

        # Create sequence templates based on performance
        sequence_templates = self._get_email_sequence_templates(grade, responded, facebook_pixel)

        emails = []
        for i, template in enumerate(sequence_templates):
            email_data = await self._generate_single_email(
                lead_data, audit_data, template, tone, include_audit_link, i + 1
            )
            email_data["sequence_position"] = i + 1
            email_data["send_delay_days"] = sequence_days[i] if i < len(sequence_days) else sequence_days[-1] + (i - len(sequence_days) + 1) * 7
            emails.append(email_data)

        return emails

    async def generate_linkedin_sequence(self, lead_data: Dict, audit_data: Dict, tone: str = "professional", include_audit_link: bool = True) -> List[Dict[str, str]]:
        """Generate a 3-LinkedIn message sequence based on audit results."""

        # Define LinkedIn sequence strategy
        grade = audit_data.get("grade", "C")
        responded = audit_data.get("responded", False)
        facebook_pixel = audit_data.get("facebook_pixel_detected", False)

        sequence_templates = self._get_linkedin_sequence_templates(grade, responded, facebook_pixel)

        messages = []
        for i, template in enumerate(sequence_templates):
            message_data = await self._generate_single_linkedin_message(
                lead_data, audit_data, template, tone, include_audit_link, i + 1
            )
            message_data["sequence_position"] = i + 1
            message_data["send_delay_days"] = [0, 5, 12][i]  # LinkedIn spacing: immediate, 5 days, 12 days
            messages.append(message_data)

        return messages

    async def generate_email(self, lead_data: Dict, audit_data: Dict, tone: str = "professional", include_audit_link: bool = True) -> Dict[str, str]:
        """Generate a personalized cold email based on audit results."""

        # Extract key data points
        company_name = lead_data.get("company", "your company")
        first_name = lead_data.get("first_name", "")
        website = lead_data.get("website", "")

        # Audit insights
        grade = audit_data.get("grade", "C")
        response_time = audit_data.get("response_time_human", "slow")
        responded = audit_data.get("responded", False)
        conversion_rate = audit_data.get("conversion_rate_current", 50)
        facebook_pixel = audit_data.get("facebook_pixel_detected", False)

        # Create personalization context
        context = {
            "company_name": company_name,
            "first_name": first_name,
            "website": website,
            "grade": grade,
            "response_time": response_time,
            "responded": responded,
            "conversion_rate": conversion_rate,
            "facebook_pixel": facebook_pixel,
            "tone": tone
        }

        # Generate email using OpenAI
        prompt = self._create_email_prompt(context, include_audit_link)

        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert B2B sales copywriter specializing in lead generation and conversion optimization. Write compelling, personalized cold emails that get responses."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=800
            )

            content = response.choices[0].message.content.strip()

            # Parse the response to extract subject and body
            lines = content.split('\n')
            subject_line = ""
            message_body = ""

            for i, line in enumerate(lines):
                if line.startswith("Subject:"):
                    subject_line = line.replace("Subject:", "").strip()
                elif line.startswith("Body:") or (subject_line and i > 0):
                    message_body = '\n'.join(lines[i:]).replace("Body:", "").strip()
                    break

            if not subject_line:
                subject_line = f"Quick question about {company_name}'s lead response"

            if not message_body:
                message_body = content

            return {
                "subject_line": subject_line,
                "message_body": message_body,
                "personalization_data": context
            }

        except Exception as e:
            logger.error(f"Error generating email: {e}")
            raise HTTPException(status_code=500, detail=f"Error generating email: {str(e)}")

    async def generate_linkedin_message(self, lead_data: Dict, audit_data: Dict, tone: str = "professional", include_audit_link: bool = True) -> Dict[str, str]:
        """Generate a personalized LinkedIn message based on audit results."""

        # Extract key data points
        company_name = lead_data.get("company", "your company")
        first_name = lead_data.get("first_name", "")
        title = lead_data.get("title", "")

        # Audit insights
        grade = audit_data.get("grade", "C")
        response_time = audit_data.get("response_time_human", "slow")
        conversion_rate = audit_data.get("conversion_rate_current", 50)

        # Create personalization context
        context = {
            "company_name": company_name,
            "first_name": first_name,
            "title": title,
            "grade": grade,
            "response_time": response_time,
            "conversion_rate": conversion_rate,
            "tone": tone
        }

        # Generate LinkedIn message using OpenAI
        prompt = self._create_linkedin_prompt(context, include_audit_link)

        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert LinkedIn outreach specialist. Write concise, professional LinkedIn messages that start conversations and get responses. Keep messages under 300 characters when possible."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=400
            )

            message_body = response.choices[0].message.content.strip()

            return {
                "message_body": message_body,
                "personalization_data": context
            }

        except Exception as e:
            logger.error(f"Error generating LinkedIn message: {e}")
            raise HTTPException(status_code=500, detail=f"Error generating LinkedIn message: {str(e)}")

    def _create_email_prompt(self, context: Dict, include_audit_link: bool) -> str:
        """Create the prompt for email generation."""

        audit_insight = self._get_audit_insight(context)

        prompt = f"""
Write a personalized cold email for lead generation. Here's the context:

Company: {context['company_name']}
Contact: {context['first_name']}
Website: {context.get('website', 'N/A')}
Tone: {context['tone']}

Audit Results:
- Response Grade: {context['grade']}
- Response Time: {context['response_time']}
- Current Conversion Rate: {context['conversion_rate']}%
- Facebook Pixel: {'Detected' if context['facebook_pixel'] else 'Not Detected'}

Key Insight: {audit_insight}

Requirements:
1. Write a compelling subject line that mentions their specific issue
2. Keep the email under 150 words
3. Be specific about their audit results without being too technical
4. Include a clear call-to-action
5. Sound helpful, not salesy
6. Use their company name and first name naturally
{"7. Mention that we have a detailed audit report available" if include_audit_link else ""}

Format:
Subject: [subject line]

Body:
[email body]
"""
        return prompt

    def _create_linkedin_prompt(self, context: Dict, include_audit_link: bool) -> str:
        """Create the prompt for LinkedIn message generation."""

        audit_insight = self._get_audit_insight(context)

        prompt = f"""
Write a personalized LinkedIn connection message or direct message. Here's the context:

Company: {context['company_name']}
Contact: {context['first_name']}
Title: {context.get('title', 'N/A')}
Tone: {context['tone']}

Audit Results:
- Response Grade: {context['grade']}
- Response Time: {context['response_time']}
- Current Conversion Rate: {context['conversion_rate']}%

Key Insight: {audit_insight}

Requirements:
1. Keep it under 300 characters (LinkedIn limit)
2. Be conversational and professional
3. Mention their specific performance issue briefly
4. Include a soft call-to-action
5. Don't be pushy or salesy
{"6. Mention we have insights to share" if include_audit_link else ""}

Write just the message body, no subject line needed.
"""
        return prompt

    def _get_audit_insight(self, context: Dict) -> str:
        """Generate a key insight based on audit data."""

        grade = context['grade']
        conversion_rate = context['conversion_rate']
        facebook_pixel = context['facebook_pixel']

        if grade in ['F']:
            return "They're not responding to leads at all, missing 100% of opportunities"
        elif grade in ['D', 'C']:
            return f"Their slow response time is costing them {85 - conversion_rate}% of potential conversions"
        elif not facebook_pixel:
            return "They're missing Facebook Pixel tracking, losing retargeting opportunities"
        else:
            return "They have good response times but could optimize further"

    def _get_email_sequence_templates(self, grade: str, responded: bool, facebook_pixel: bool) -> List[Dict[str, str]]:
        """Get email sequence templates based on audit performance."""

        # Determine primary issue for sequence focus
        primary_issue = self._get_primary_issue(grade, responded, facebook_pixel)

        if primary_issue == "no_response":
            # No response sequence - audit report focused
            return [
                {
                    "type": "audit_delivery",
                    "focus": "shocking_audit_results",
                    "cta": "view_audit_report"
                },
                {
                    "type": "audit_breakdown",
                    "focus": "what_audit_means",
                    "cta": "understand_impact"
                },
                {
                    "type": "competitor_audit_comparison",
                    "focus": "vs_competitors_audit",
                    "cta": "see_benchmark_report"
                },
                {
                    "type": "audit_action_plan",
                    "focus": "fixing_audit_issues",
                    "cta": "get_action_plan"
                },
                {
                    "type": "audit_consultation",
                    "focus": "discuss_audit_findings",
                    "cta": "schedule_audit_review"
                }
            ]
        elif primary_issue == "facebook_pixel":
            # Facebook Pixel focused sequence
            return [
                {
                    "type": "pixel_audit_delivery",
                    "focus": "missing_pixel_audit",
                    "cta": "view_pixel_audit"
                },
                {
                    "type": "pixel_revenue_impact",
                    "focus": "pixel_lost_revenue",
                    "cta": "calculate_pixel_roi"
                },
                {
                    "type": "pixel_competitor_advantage",
                    "focus": "competitors_using_pixels",
                    "cta": "see_pixel_benchmark"
                },
                {
                    "type": "pixel_setup_guide",
                    "focus": "how_to_install_pixel",
                    "cta": "get_pixel_guide"
                },
                {
                    "type": "pixel_consultation",
                    "focus": "pixel_strategy_session",
                    "cta": "book_pixel_consultation"
                }
            ]
        elif grade in ['D', 'C']:
            # Poor response time sequence - audit focused
            return [
                {
                    "type": "audit_delivery",
                    "focus": "response_time_audit",
                    "cta": "view_full_audit"
                },
                {
                    "type": "audit_business_impact",
                    "focus": "audit_revenue_loss",
                    "cta": "see_impact_calculation"
                },
                {
                    "type": "audit_improvement_plan",
                    "focus": "audit_recommendations",
                    "cta": "download_improvement_plan"
                },
                {
                    "type": "audit_success_story",
                    "focus": "similar_audit_transformation",
                    "cta": "see_before_after_audit"
                },
                {
                    "type": "audit_consultation",
                    "focus": "implement_audit_fixes",
                    "cta": "schedule_implementation_call"
                }
            ]
        else:
            # Good performance sequence - optimization audit focused
            return [
                {
                    "type": "positive_audit_delivery",
                    "focus": "strong_audit_results",
                    "cta": "view_audit_report"
                },
                {
                    "type": "audit_optimization_opportunities",
                    "focus": "audit_fine_tuning",
                    "cta": "see_optimization_audit"
                },
                {
                    "type": "industry_audit_benchmark",
                    "focus": "audit_competitive_position",
                    "cta": "compare_audit_results"
                },
                {
                    "type": "advanced_audit_insights",
                    "focus": "next_level_audit_tips",
                    "cta": "get_advanced_audit"
                },
                {
                    "type": "audit_partnership",
                    "focus": "ongoing_audit_monitoring",
                    "cta": "discuss_audit_partnership"
                }
            ]

    def _get_primary_issue(self, grade: str, responded: bool, facebook_pixel: bool) -> str:
        """Determine the primary issue to focus the sequence on."""
        if not responded:
            return "no_response"
        elif not facebook_pixel:
            return "facebook_pixel"
        elif grade in ['D', 'C']:
            return "poor_response"
        else:
            return "optimization"

    def _get_linkedin_sequence_templates(self, grade: str, responded: bool, facebook_pixel: bool) -> List[Dict[str, str]]:
        """Get LinkedIn sequence templates based on audit performance."""

        # Determine primary issue for LinkedIn sequence
        primary_issue = self._get_primary_issue(grade, responded, facebook_pixel)

        if primary_issue == "no_response":
            return [
                {
                    "type": "audit_connection_request",
                    "focus": "shocking_audit_findings",
                    "cta": "connect_view_audit"
                },
                {
                    "type": "audit_insight_share",
                    "focus": "audit_business_impact",
                    "cta": "discuss_audit"
                },
                {
                    "type": "audit_final_offer",
                    "focus": "audit_action_needed",
                    "cta": "review_audit_together"
                }
            ]
        elif primary_issue == "facebook_pixel":
            return [
                {
                    "type": "pixel_audit_connection",
                    "focus": "pixel_audit_findings",
                    "cta": "connect_see_pixel_audit"
                },
                {
                    "type": "pixel_opportunity_share",
                    "focus": "pixel_revenue_opportunity",
                    "cta": "discuss_pixel_strategy"
                },
                {
                    "type": "pixel_consultation_offer",
                    "focus": "pixel_implementation_help",
                    "cta": "schedule_pixel_call"
                }
            ]
        elif grade in ['D', 'C']:
            return [
                {
                    "type": "audit_connection_request",
                    "focus": "response_audit_opportunity",
                    "cta": "connect_see_audit"
                },
                {
                    "type": "audit_findings_share",
                    "focus": "specific_audit_insights",
                    "cta": "discuss_audit_results"
                },
                {
                    "type": "audit_implementation_offer",
                    "focus": "audit_improvement_plan",
                    "cta": "schedule_audit_call"
                }
            ]
        else:
            return [
                {
                    "type": "positive_audit_connection",
                    "focus": "strong_audit_compliment",
                    "cta": "connect_share_audit"
                },
                {
                    "type": "audit_optimization_tip",
                    "focus": "audit_enhancement_idea",
                    "cta": "share_optimization_audit"
                },
                {
                    "type": "audit_partnership_offer",
                    "focus": "ongoing_audit_monitoring",
                    "cta": "explore_audit_partnership"
                }
            ]

    async def _generate_single_email(self, lead_data: Dict, audit_data: Dict, template: Dict, tone: str, include_audit_link: bool, sequence_position: int) -> Dict[str, str]:
        """Generate a single email based on template and sequence position."""

        # Extract key data points
        company_name = lead_data.get("company", "your company")
        first_name = lead_data.get("first_name", "")
        website = lead_data.get("website", "")

        # Audit insights
        grade = audit_data.get("grade", "C")
        response_time = audit_data.get("response_time_human", "slow")
        responded = audit_data.get("responded", False)
        conversion_rate = audit_data.get("conversion_rate_current", 50)
        facebook_pixel = audit_data.get("facebook_pixel_detected", False)

        # Create personalization context
        context = {
            "company_name": company_name,
            "first_name": first_name,
            "website": website,
            "grade": grade,
            "response_time": response_time,
            "responded": responded,
            "conversion_rate": conversion_rate,
            "facebook_pixel": facebook_pixel,
            "tone": tone,
            "template": template,
            "sequence_position": sequence_position
        }

        # Generate email using OpenAI
        prompt = self._create_sequence_email_prompt(context, include_audit_link)

        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": f"You are an expert B2B sales copywriter specializing in lead generation sequences. Write email #{sequence_position} in a 5-email sequence. Each email should build on the previous ones and move the prospect closer to a consultation."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=800
            )

            content = response.choices[0].message.content.strip()

            # Parse the response to extract subject and body
            lines = content.split('\n')
            subject_line = ""
            message_body = ""

            for i, line in enumerate(lines):
                if line.startswith("Subject:"):
                    subject_line = line.replace("Subject:", "").strip()
                elif line.startswith("Body:") or (subject_line and i > 0):
                    message_body = '\n'.join(lines[i:]).replace("Body:", "").strip()
                    break

            if not subject_line:
                subject_line = f"Email #{sequence_position}: {company_name} lead response insights"

            if not message_body:
                message_body = content

            return {
                "subject_line": subject_line,
                "message_body": message_body,
                "personalization_data": context,
                "template_type": template["type"],
                "template_focus": template["focus"]
            }

        except Exception as e:
            logger.error(f"Error generating sequence email: {e}")
            raise HTTPException(status_code=500, detail=f"Error generating sequence email: {str(e)}")

    async def _generate_single_linkedin_message(self, lead_data: Dict, audit_data: Dict, template: Dict, tone: str, include_audit_link: bool, sequence_position: int) -> Dict[str, str]:
        """Generate a single LinkedIn message based on template and sequence position."""

        # Extract key data points
        company_name = lead_data.get("company", "your company")
        first_name = lead_data.get("first_name", "")
        title = lead_data.get("title", "")

        # Audit insights
        grade = audit_data.get("grade", "C")
        response_time = audit_data.get("response_time_human", "slow")
        conversion_rate = audit_data.get("conversion_rate_current", 50)

        # Create personalization context
        context = {
            "company_name": company_name,
            "first_name": first_name,
            "title": title,
            "grade": grade,
            "response_time": response_time,
            "conversion_rate": conversion_rate,
            "tone": tone,
            "template": template,
            "sequence_position": sequence_position
        }

        # Generate LinkedIn message using OpenAI
        prompt = self._create_sequence_linkedin_prompt(context, include_audit_link)

        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": f"You are an expert LinkedIn outreach specialist. Write LinkedIn message #{sequence_position} in a 3-message sequence. Keep messages under 300 characters and build rapport progressively."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=400
            )

            message_body = response.choices[0].message.content.strip()

            return {
                "message_body": message_body,
                "personalization_data": context,
                "template_type": template["type"],
                "template_focus": template["focus"]
            }

        except Exception as e:
            logger.error(f"Error generating sequence LinkedIn message: {e}")
            raise HTTPException(status_code=500, detail=f"Error generating sequence LinkedIn message: {str(e)}")

    def _create_sequence_email_prompt(self, context: Dict, include_audit_link: bool) -> str:
        """Create the prompt for sequence email generation."""

        template = context['template']
        sequence_position = context['sequence_position']

        # Get sequence-specific guidance
        sequence_guidance = {
            1: "This is the first email - introduce the audit findings and create curiosity",
            2: "This is the second follow-up - provide more value and address potential objections",
            3: "This is the third follow-up - share educational content or case studies",
            4: "This is the fourth follow-up - create urgency and social proof",
            5: "This is the final follow-up - last chance offer with clear next steps"
        }

        audit_insight = self._get_audit_insight(context)

        # Get audit-specific context
        audit_focus = self._get_audit_focus_context(context, template)

        prompt = f"""
Write email #{sequence_position} in a 5-email sequence focused on a lead response audit report. Here's the context:

Company: {context['company_name']}
Contact: {context['first_name']}
Website: {context.get('website', 'N/A')}
Tone: {context['tone']}

AUDIT REPORT FINDINGS:
- Overall Grade: {context['grade']}
- Response Time: {context['response_time']}
- Current Conversion Rate: {context['conversion_rate']}%
- Facebook Pixel Status: {'Detected' if context['facebook_pixel'] else 'Missing - Major Issue'}
- Responded to Test Lead: {'Yes' if context['responded'] else 'No - Critical Problem'}

Email Template: {template['type']}
Focus: {template['focus']}
CTA: {template['cta']}
Audit Focus: {audit_focus}

Sequence Guidance: {sequence_guidance.get(sequence_position, 'Continue building the relationship')}

CRITICAL REQUIREMENTS:
1. This email MUST focus on the audit report and its findings
2. The subject line should reference the audit or its implications
3. The main message should be about what the audit revealed about their business
4. Explain what the audit findings mean for their revenue/business
5. Always include a clear path to view or discuss the full audit report
6. Reference specific audit metrics (grade, response time, conversion rate)
7. Keep under 150 words but make the audit findings the hero
8. Sound like a consultant delivering important business intelligence
{"9. Include a direct link or reference to viewing the complete audit report" if include_audit_link else ""}

The audit report is the main value proposition - make them want to see it!

Format:
Subject: [subject line mentioning audit findings]

Body:
[email body focused on audit report and business implications]
"""
        return prompt

    def _create_sequence_linkedin_prompt(self, context: Dict, include_audit_link: bool) -> str:
        """Create the prompt for sequence LinkedIn message generation."""

        template = context['template']
        sequence_position = context['sequence_position']

        # Get sequence-specific guidance
        sequence_guidance = {
            1: "This is the connection request or first message - be brief and intriguing",
            2: "This is the follow-up message - provide specific value or insight",
            3: "This is the final message - soft close with clear next step"
        }

        audit_insight = self._get_audit_insight(context)

        # Get audit-specific context
        audit_focus = self._get_audit_focus_context(context, template)

        prompt = f"""
Write LinkedIn message #{sequence_position} in a 3-message sequence focused on a lead response audit report. Here's the context:

Company: {context['company_name']}
Contact: {context['first_name']}
Title: {context.get('title', 'N/A')}
Tone: {context['tone']}

AUDIT REPORT FINDINGS:
- Overall Grade: {context['grade']}
- Response Time: {context['response_time']}
- Current Conversion Rate: {context['conversion_rate']}%
- Facebook Pixel: {'Detected' if context.get('facebook_pixel', False) else 'Missing'}
- Responded to Test: {'Yes' if context.get('responded', False) else 'No'}

Message Template: {template['type']}
Focus: {template['focus']}
CTA: {template['cta']}
Audit Focus: {audit_focus}

Sequence Guidance: {sequence_guidance.get(sequence_position, 'Continue the conversation')}

CRITICAL REQUIREMENTS:
1. Keep under 300 characters (LinkedIn limit)
2. MUST reference the audit report or its findings
3. Focus on what the audit revealed about their business
4. Mention specific audit metrics (grade, response time, etc.)
5. Make them curious about the full audit report
6. Be conversational but professional
7. Include a soft call-to-action related to the audit
{"8. Reference the complete audit report being available" if include_audit_link else ""}

The audit report is your main value - lead with it!

Write just the message body, no subject line needed.
"""
        return prompt

    def _get_audit_focus_context(self, context: Dict, template: Dict) -> str:
        """Get specific audit focus context based on template and findings."""

        template_type = template.get('type', '')
        focus = template.get('focus', '')

        # Facebook Pixel specific contexts
        if 'pixel' in template_type:
            if not context.get('facebook_pixel', False):
                return f"Their website is missing Facebook Pixel tracking entirely - this means they're losing all retargeting opportunities and can't track conversions properly. The audit shows this is costing them significant revenue."
            else:
                return f"The audit detected their Facebook Pixel but found optimization opportunities in their tracking setup that could improve their marketing ROI."

        # Response time specific contexts
        elif 'audit_delivery' in template_type or 'audit_reveal' in template_type:
            if not context.get('responded', False):
                return f"The audit revealed a critical issue: they're not responding to leads at all. Our test lead submission received zero response, meaning they're losing 100% of potential customers."
            elif context.get('grade') in ['D', 'C']:
                return f"The audit shows they're responding in {context.get('response_time', 'slow time')} with a {context.get('grade')} grade, significantly slower than industry leaders who respond in under 5 minutes."
            else:
                return f"The audit shows strong performance with {context.get('response_time', 'fast')} response time and {context.get('grade')} grade, but identified specific optimization opportunities."

        # Business impact contexts
        elif 'business_impact' in focus or 'revenue' in focus:
            current_rate = context.get('conversion_rate', 50)
            potential_rate = 85
            lost_percentage = potential_rate - current_rate
            return f"The audit calculates they're losing approximately {lost_percentage}% of potential conversions due to response time issues. For their business size, this represents significant annual revenue loss."

        # Competitor comparison contexts
        elif 'competitor' in focus or 'benchmark' in focus:
            return f"The audit benchmarked them against industry competitors and found they're underperforming in key areas that directly impact lead conversion rates."

        # Implementation/action contexts
        elif 'action' in focus or 'implementation' in focus:
            return f"The audit identified specific, actionable steps they can take to improve their lead response performance and increase conversion rates."

        # Default audit context
        else:
            return f"The comprehensive audit analyzed their lead response system and identified key opportunities for improvement that could significantly impact their business results."

# Initialize the generator
outreach_generator = OutreachGenerator()

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "outreach-agent"}

@app.post("/generate-outreach", response_model=OutreachResponse)
async def generate_outreach(request: OutreachRequest):
    """Generate outreach messages for a lead based on their audit report."""

    try:
        # Fetch lead data
        lead_response = supabase.table("leads").select("*").eq("id", request.lead_id).execute()
        if not lead_response.data:
            raise HTTPException(status_code=404, detail="Lead not found")

        lead_data = lead_response.data[0]

        # Fetch audit report data
        audit_response = supabase.table("audit_reports").select("*").eq("id", request.audit_report_id).execute()
        if not audit_response.data:
            raise HTTPException(status_code=404, detail="Audit report not found")

        audit_data = audit_response.data[0]

        generated_messages = []

        # Generate requested message types
        for message_type in request.message_types:
            if message_type == "email":
                if request.generate_sequences:
                    # Generate 5-email sequence
                    email_sequence = await outreach_generator.generate_email_sequence(
                        lead_data, audit_data, request.tone, request.include_audit_link, request.sequence_spacing_days
                    )

                    # Save each email in the sequence
                    for email_data in email_sequence:
                        message_id = str(uuid.uuid4())
                        message_record = {
                            "id": message_id,
                            "lead_id": request.lead_id,
                            "audit_report_id": request.audit_report_id,
                            "message_type": "email",
                            "status": "draft",
                            "subject_line": email_data["subject_line"],
                            "message_body": email_data["message_body"],
                            "personalization_data": email_data["personalization_data"],
                            "to_email": lead_data.get("email"),
                            "ai_model_used": outreach_generator.model,
                            "sequence_position": email_data["sequence_position"],
                            "send_delay_days": email_data["send_delay_days"],
                            "template_type": email_data["template_type"],
                            "template_focus": email_data["template_focus"],
                            "created_at": datetime.utcnow().isoformat()
                        }

                        supabase.table("outreach_messages").insert(message_record).execute()
                        generated_messages.append(message_record)
                else:
                    # Generate single email
                    email_data = await outreach_generator.generate_email(
                        lead_data, audit_data, request.tone, request.include_audit_link
                    )

                    message_id = str(uuid.uuid4())
                    message_record = {
                        "id": message_id,
                        "lead_id": request.lead_id,
                        "audit_report_id": request.audit_report_id,
                        "message_type": "email",
                        "status": "draft",
                        "subject_line": email_data["subject_line"],
                        "message_body": email_data["message_body"],
                        "personalization_data": email_data["personalization_data"],
                        "to_email": lead_data.get("email"),
                        "ai_model_used": outreach_generator.model,
                        "sequence_position": 1,
                        "send_delay_days": 0,
                        "created_at": datetime.utcnow().isoformat()
                    }

                    supabase.table("outreach_messages").insert(message_record).execute()
                    generated_messages.append(message_record)

            elif message_type == "linkedin":
                if request.generate_sequences:
                    # Generate 3-LinkedIn sequence
                    linkedin_sequence = await outreach_generator.generate_linkedin_sequence(
                        lead_data, audit_data, request.tone, request.include_audit_link
                    )

                    # Save each LinkedIn message in the sequence
                    for linkedin_data in linkedin_sequence:
                        message_id = str(uuid.uuid4())
                        message_record = {
                            "id": message_id,
                            "lead_id": request.lead_id,
                            "audit_report_id": request.audit_report_id,
                            "message_type": "linkedin",
                            "status": "draft",
                            "message_body": linkedin_data["message_body"],
                            "personalization_data": linkedin_data["personalization_data"],
                            "linkedin_profile_url": lead_data.get("linkedin"),
                            "ai_model_used": outreach_generator.model,
                            "sequence_position": linkedin_data["sequence_position"],
                            "send_delay_days": linkedin_data["send_delay_days"],
                            "template_type": linkedin_data["template_type"],
                            "template_focus": linkedin_data["template_focus"],
                            "created_at": datetime.utcnow().isoformat()
                        }

                        supabase.table("outreach_messages").insert(message_record).execute()
                        generated_messages.append(message_record)
                else:
                    # Generate single LinkedIn message
                    linkedin_data = await outreach_generator.generate_linkedin_message(
                        lead_data, audit_data, request.tone, request.include_audit_link
                    )

                    message_id = str(uuid.uuid4())
                    message_record = {
                        "id": message_id,
                        "lead_id": request.lead_id,
                        "audit_report_id": request.audit_report_id,
                        "message_type": "linkedin",
                        "status": "draft",
                        "message_body": linkedin_data["message_body"],
                        "personalization_data": linkedin_data["personalization_data"],
                        "linkedin_profile_url": lead_data.get("linkedin"),
                        "ai_model_used": outreach_generator.model,
                        "sequence_position": 1,
                        "send_delay_days": 0,
                        "created_at": datetime.utcnow().isoformat()
                    }

                    supabase.table("outreach_messages").insert(message_record).execute()
                    generated_messages.append(message_record)

        # Update lead state
        supabase.table("lead_states").upsert({
            "lead_id": request.lead_id,
            "outreach_generated": True,
            "outreach_generated_at": datetime.utcnow().isoformat(),
            "current_state": "outreach_generated"
        }).execute()

        return OutreachResponse(
            success=True,
            message=f"Generated {len(generated_messages)} outreach messages",
            generated_messages=generated_messages,
            lead_id=request.lead_id,
            audit_report_id=request.audit_report_id
        )

    except Exception as e:
        logger.error(f"Error generating outreach: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating outreach: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8007)

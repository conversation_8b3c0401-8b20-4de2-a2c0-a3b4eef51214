# Modern Facebook Ads Strategy for Roofing Companies - Key Updates

## Overview

This document summarizes the critical updates made to the Facebook Lead Ads SOP based on current algorithm behavior and best practices for 2024.

---

## 🎯 **1. TARGETING REVOLUTION: Location-Only Approach**

### ❌ **OLD WAY (Doesn't Work Anymore):**
```
- Age: 35-65
- Income: $50,000+
- Interests: Home improvement, roofing
- Behaviors: Homeowners, recently moved
- Demographics: Home value, property age
```

### ✅ **NEW WAY (Algorithm Optimized):**
```
- Age: 25-65 (broad range)
- Location: Within 25 miles of service area
- That's it - NO other targeting
```

### **Why This Works:**
- **Facebook's algorithm is 10X better** at finding your ideal customer than manual targeting
- **Local businesses have smaller audiences** - broad targeting prevents audience exhaustion
- **Algorithm learns from conversions** and finds similar people automatically
- **Lower costs and better delivery** with broader audiences
- **No competition between narrow segments** that drive up costs

---

## 🎨 **2. CREATIVE STRATEGY: 3-2-2 Dynamic Ads Method**

### ❌ **OLD WAY (Self-Competition):**
```
- Create 12 separate ads
- Each ad competes against others
- Budget splits across all ads
- Higher costs due to self-competition
- Difficult to manage and optimize
```

### ✅ **NEW WAY (Dynamic Optimization):**
```
3 Primary Text Variations:
- Emergency/Urgency focused
- Quality/Trust focused
- Value/Benefit focused

2 Headline Variations:
- Direct call-to-action
- Benefit-focused

2 Creative Assets:
- Short-form video (15-30 seconds)
- High-impact image

= 12 automatic combinations in ONE ad
```

### **Why This Works:**
- **Facebook tests all combinations automatically** and shifts budget to winners
- **No self-competition** between ads
- **Lower costs** and better delivery
- **Easier management** - one ad instead of 12
- **Algorithm learns faster** with consolidated data

---

## 📱 **3. VIDEO STRATEGY: Short-Form Content Dominance**

### ❌ **OLD WAY (Outdated):**
```
- 60-90 second videos
- Landscape format
- Detailed explanations
- Desktop-optimized
```

### ✅ **NEW WAY (TikTok-Influenced):**
```
- 15-30 seconds ONLY
- Square (1080x1080) or vertical (9:16)
- Hook in first 3 seconds
- Mobile-optimized with captions
```

### **Why This Works:**
- **Facebook competing with TikTok** = algorithm preference for short-form
- **Better delivery and lower costs** for short videos
- **Higher engagement rates** on mobile devices
- **Attention spans are shorter** - get to the point fast

### **Video Structure:**
```
0-3 seconds: Hook (problem/urgency)
4-15 seconds: Solution demonstration  
16-30 seconds: Call-to-action and contact
```

---

## 🎯 **4. CAMPAIGN OBJECTIVES: Use What You Want**

### ❌ **OLD THINKING (Wrong):**
```
"I'll use Traffic objective because some will convert"
"Engagement objective will build awareness"
"Reach objective is cheaper"
```

### ✅ **NEW REALITY (Algorithm Truth):**
```
Traffic Objective = Optimizes for clicks (window shoppers)
Lead Generation Objective = Optimizes for form fills (actual leads)
```

### **Critical Principle:**
**Facebook's algorithm optimizes for EXACTLY what you ask for - nothing else.**

- **Traffic campaigns** get you people who click but don't convert (tire kickers)
- **Lead generation campaigns** get you people who fill out forms
- **The algorithm learns different behaviors** for each objective

### **Example:**
*"I'm a window shopper online - I love clicking ads but rarely buy. Facebook shows me ALL traffic campaigns because I match that behavior pattern."*

---

## 🏠 **5. IN-PLATFORM LEAD GENERATION (Preferred)**

### ❌ **OLD WAY (External Landing Pages):**
```
- Send traffic to website forms
- Multiple steps and page loads
- Higher abandonment rates
- Algorithm doesn't prefer external traffic
```

### ✅ **NEW WAY (Facebook Lead Forms):**
```
- Keep users on Facebook platform
- Pre-filled information (faster completion)
- Mobile-optimized experience
- Algorithm prefers keeping users on platform
- Better delivery and lower costs
```

### **Why Facebook Prefers This:**
- **Keeps users on Facebook** = more ad revenue for them
- **Better user experience** = higher completion rates
- **Algorithm rewards** campaigns that keep users engaged on platform

---

## 💰 **6. BIDDING STRATEGY: Let Algorithm Optimize**

### ❌ **OLD WAY (Manual Control):**
```
- Set bid caps
- Use target cost
- Manual bidding strategies
- Constant bid adjustments
```

### ✅ **NEW WAY (Algorithm Trust):**
```
Week 1-2: Lowest Cost (no restrictions)
Week 3-4: Cost Cap (only if needed)
Month 2+: Value optimization (if tracking revenue)
```

### **Why This Works:**
- **Algorithm is better at bidding** than manual controls
- **Bid caps restrict learning** and limit delivery
- **Let algorithm find optimal cost** before adding restrictions
- **Focus on lead quality** over cost metrics

---

## 📊 **7. SIMPLIFIED CAMPAIGN STRUCTURE**

### ❌ **OLD WAY (Over-Complicated):**
```
- 10+ campaigns for different audiences
- Multiple ad sets per campaign
- Complex targeting combinations
- Difficult to manage and optimize
```

### ✅ **NEW WAY (Streamlined):**
```
- Emergency Response (Broad Location + 3-2-2 Ads)
- Roof Replacement (Broad Location + 3-2-2 Ads)  
- Maintenance Services (Broad Location + 3-2-2 Ads)
- Retargeting (Custom Audiences + 3-2-2 Ads)
```

### **Benefits:**
- **Less self-competition** between campaigns
- **Easier budget management** and optimization
- **Algorithm learns faster** with more data per campaign
- **Cleaner reporting** and performance analysis

---

## ⏰ **8. MANAGEMENT PHILOSOPHY: Don't Over-Optimize**

### ❌ **OLD HABITS (Harmful):**
```
- Daily budget adjustments
- Pause ads after 1 bad day
- Constant targeting changes
- Micro-manage every metric
```

### ✅ **NEW APPROACH (Algorithm-Friendly):**
```
- Let algorithm learn 3-7 days minimum
- Focus on trends, not daily fluctuations
- Prioritize lead quality over cost metrics
- Make fewer, more strategic changes
```

### **Daily Management (25 minutes total):**
- **Morning (10 min):** Check leads, weather, tracking
- **Midday (5 min):** Form completion, delivery issues
- **Evening (10 min):** Performance review, quality feedback

---

## 🚀 **IMPLEMENTATION ROADMAP**

### **Week 1: Foundation**
- [ ] Set up broad location-only targeting
- [ ] Create 3-2-2 dynamic ads for each campaign
- [ ] Use Lead Generation objective only
- [ ] Start with Lowest Cost bidding

### **Week 2: Optimization**
- [ ] Monitor lead quality feedback
- [ ] Let algorithm learn (don't over-adjust)
- [ ] Focus on short-form video content
- [ ] Use in-platform lead forms

### **Week 3-4: Refinement**
- [ ] Add cost controls only if needed
- [ ] Scale winning combinations
- [ ] Refresh creative if performance declines
- [ ] Optimize based on conversion data

---

## 📈 **EXPECTED RESULTS**

### **Immediate Benefits (Week 1-2):**
- **Lower cost per lead** due to broader targeting
- **Better delivery** with algorithm-friendly approach
- **Easier management** with simplified structure

### **Long-term Benefits (Month 2+):**
- **Higher lead quality** as algorithm learns your customers
- **Improved ROAS** with value optimization
- **Scalable system** that works consistently

---

## 🎯 **KEY TAKEAWAYS**

1. **Trust the Algorithm** - It's better than manual controls
2. **Broad Targeting Wins** - Location-only for local businesses
3. **3-2-2 Method** - One dynamic ad beats 12 separate ads
4. **Short-Form Video** - 15-30 seconds gets better delivery
5. **Use Correct Objective** - Lead Generation for leads, not Traffic
6. **In-Platform Forms** - Keep users on Facebook for better results
7. **Don't Over-Optimize** - Let algorithm learn before making changes

---

This modern approach aligns with Facebook's current algorithm behavior and delivers better results with less work. The key is working WITH the algorithm instead of trying to outsmart it.

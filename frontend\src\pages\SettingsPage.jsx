import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Breadcrumbs,
  Link,
  Paper,
} from '@mui/material';
import {
  Home as HomeIcon,
  Settings as SettingsIcon,
  Person as PersonIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Monitor as MonitorIcon,
} from '@mui/icons-material';
import { LeadDataSettings } from '../components/Settings';
import SystemStatusPanel from '../components/SystemStatus/SystemStatusPanel';

/**
 * Settings Page
 *
 * This page displays various settings for the application.
 */
const SettingsPage = () => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 3 }}>
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link
            underline="hover"
            color="inherit"
            href="/"
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Home
          </Link>
          <Typography
            sx={{ display: 'flex', alignItems: 'center' }}
            color="text.primary"
          >
            <SettingsIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Settings
          </Typography>
        </Breadcrumbs>

        <Typography variant="h4" gutterBottom>
          Settings
        </Typography>

        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab icon={<PersonIcon />} label="Lead Data" />
            <Tab icon={<MonitorIcon />} label="System Status" />
            <Tab icon={<NotificationsIcon />} label="Notifications" />
            <Tab icon={<SecurityIcon />} label="Security" />
          </Tabs>

          <Box sx={{ p: 0 }}>
            {activeTab === 0 && <LeadDataSettings />}
            {activeTab === 1 && (
              <Box sx={{ p: 3 }}>
                <SystemStatusPanel />
              </Box>
            )}
            {activeTab === 2 && (
              <Box sx={{ p: 3 }}>
                <Typography variant="h5" gutterBottom>
                  Notification Settings
                </Typography>
                <Typography variant="body1" color="textSecondary">
                  Configure your notification preferences. (Coming soon)
                </Typography>
              </Box>
            )}
            {activeTab === 3 && (
              <Box sx={{ p: 3 }}>
                <Typography variant="h5" gutterBottom>
                  Security Settings
                </Typography>
                <Typography variant="body1" color="textSecondary">
                  Configure your security settings. (Coming soon)
                </Typography>
              </Box>
            )}
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default SettingsPage;

import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  Button,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  Launch as LaunchIcon,
  Storage as StorageIcon,
  Api as ApiIcon,
  Cloud as CloudIcon,
  Speed as SpeedIcon
} from '@mui/icons-material';

/**
 * System Status Panel Component
 * 
 * Monitors the health and status of all system services
 */
const SystemStatusPanel = () => {
  const [loading, setLoading] = useState(false);
  const [lastChecked, setLastChecked] = useState(null);
  const [services, setServices] = useState([
    {
      name: 'API Gateway',
      url: 'http://localhost:8000',
      endpoint: '/health',
      status: 'unknown',
      responseTime: null,
      description: 'Main API gateway and routing'
    },
    {
      name: 'Form Discovery',
      url: 'http://localhost:8001',
      endpoint: '/health',
      status: 'unknown',
      responseTime: null,
      description: 'Form discovery and submission service'
    },
    {
      name: 'Response Monitoring',
      url: 'http://localhost:8002',
      endpoint: '/health',
      status: 'unknown',
      responseTime: null,
      description: 'Email and SMS response monitoring'
    },
    {
      name: 'Lead Management',
      url: 'http://localhost:8003',
      endpoint: '/health',
      status: 'unknown',
      responseTime: null,
      description: 'Lead data management and CRM'
    },
    {
      name: 'Analytics',
      url: 'http://localhost:8004',
      endpoint: '/health',
      status: 'unknown',
      responseTime: null,
      description: 'Analytics and reporting service'
    },
    {
      name: 'AI Orchestrator',
      url: 'http://localhost:8006',
      endpoint: '/health',
      status: 'unknown',
      responseTime: null,
      description: 'AI model coordination and management'
    },
    {
      name: 'Outreach Agent',
      url: 'http://localhost:8007',
      endpoint: '/health',
      status: 'unknown',
      responseTime: null,
      description: 'Outreach message generation'
    },
    {
      name: 'Lead Audit Orchestrator',
      url: 'http://localhost:8008',
      endpoint: '/health',
      status: 'unknown',
      responseTime: null,
      description: 'Automated lead audit system'
    }
  ]);

  const [systemStats, setSystemStats] = useState({
    totalServices: 8,
    healthyServices: 0,
    unhealthyServices: 0,
    avgResponseTime: 0
  });

  useEffect(() => {
    checkAllServices();
    const interval = setInterval(checkAllServices, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const checkServiceHealth = async (service) => {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${service.url}${service.endpoint}`, {
        method: 'GET',
        timeout: 5000
      });
      
      const responseTime = Date.now() - startTime;
      
      if (response.ok) {
        return {
          ...service,
          status: 'healthy',
          responseTime,
          lastError: null
        };
      } else {
        return {
          ...service,
          status: 'unhealthy',
          responseTime,
          lastError: `HTTP ${response.status}`
        };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        ...service,
        status: 'error',
        responseTime,
        lastError: error.message
      };
    }
  };

  const checkAllServices = async () => {
    setLoading(true);
    
    try {
      const healthChecks = await Promise.all(
        services.map(service => checkServiceHealth(service))
      );
      
      setServices(healthChecks);
      
      // Calculate system stats
      const healthy = healthChecks.filter(s => s.status === 'healthy').length;
      const unhealthy = healthChecks.filter(s => s.status !== 'healthy').length;
      const avgResponseTime = healthChecks
        .filter(s => s.responseTime)
        .reduce((sum, s) => sum + s.responseTime, 0) / healthChecks.length;
      
      setSystemStats({
        totalServices: healthChecks.length,
        healthyServices: healthy,
        unhealthyServices: unhealthy,
        avgResponseTime: Math.round(avgResponseTime)
      });
      
      setLastChecked(new Date());
    } catch (error) {
      console.error('Error checking services:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'success';
      case 'unhealthy': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy': return <CheckCircleIcon />;
      case 'unhealthy': return <WarningIcon />;
      case 'error': return <ErrorIcon />;
      default: return <CircularProgress size={20} />;
    }
  };

  const getOverallStatus = () => {
    if (systemStats.unhealthyServices === 0) {
      return { status: 'All Systems Operational', color: 'success' };
    } else if (systemStats.healthyServices > systemStats.unhealthyServices) {
      return { status: 'Partial Service Disruption', color: 'warning' };
    } else {
      return { status: 'Major Service Disruption', color: 'error' };
    }
  };

  const overallStatus = getOverallStatus();

  return (
    <Box>
      <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', mb: 3 }}>
        🔧 System Status
      </Typography>

      {/* Overall Status */}
      <Alert 
        severity={overallStatus.color} 
        sx={{ mb: 3 }}
        action={
          <Button
            color="inherit"
            size="small"
            startIcon={<RefreshIcon />}
            onClick={checkAllServices}
            disabled={loading}
          >
            Refresh
          </Button>
        }
      >
        <Typography variant="h6">{overallStatus.status}</Typography>
        {lastChecked && (
          <Typography variant="body2">
            Last checked: {lastChecked.toLocaleTimeString()}
          </Typography>
        )}
      </Alert>

      {/* System Statistics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <ApiIcon sx={{ color: 'primary.main', mr: 1 }} />
                <Typography variant="h6">{systemStats.totalServices}</Typography>
              </Box>
              <Typography color="text.secondary" variant="body2">
                Total Services
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <CheckCircleIcon sx={{ color: 'success.main', mr: 1 }} />
                <Typography variant="h6">{systemStats.healthyServices}</Typography>
              </Box>
              <Typography color="text.secondary" variant="body2">
                Healthy Services
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <ErrorIcon sx={{ color: 'error.main', mr: 1 }} />
                <Typography variant="h6">{systemStats.unhealthyServices}</Typography>
              </Box>
              <Typography color="text.secondary" variant="body2">
                Unhealthy Services
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <SpeedIcon sx={{ color: 'info.main', mr: 1 }} />
                <Typography variant="h6">{systemStats.avgResponseTime}ms</Typography>
              </Box>
              <Typography color="text.secondary" variant="body2">
                Avg Response Time
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Service List */}
      <Paper>
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="h6">Service Details</Typography>
        </Box>
        
        <List>
          {services.map((service, index) => (
            <ListItem key={service.name} divider={index < services.length - 1}>
              <ListItemIcon>
                {getStatusIcon(service.status)}
              </ListItemIcon>
              
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="subtitle1">{service.name}</Typography>
                    <Chip
                      label={service.status}
                      color={getStatusColor(service.status)}
                      size="small"
                    />
                    {service.responseTime && (
                      <Chip
                        label={`${service.responseTime}ms`}
                        variant="outlined"
                        size="small"
                      />
                    )}
                  </Box>
                }
                secondary={
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      {service.description}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {service.url}
                    </Typography>
                    {service.lastError && (
                      <Typography variant="caption" color="error.main" sx={{ display: 'block' }}>
                        Error: {service.lastError}
                      </Typography>
                    )}
                  </Box>
                }
              />
              
              <ListItemSecondaryAction>
                <Tooltip title="Open Service">
                  <IconButton
                    edge="end"
                    onClick={() => window.open(service.url, '_blank')}
                    size="small"
                  >
                    <LaunchIcon />
                  </IconButton>
                </Tooltip>
              </ListItemSecondaryAction>
            </ListItem>
          ))}
        </List>
      </Paper>

      {/* External Dependencies */}
      <Paper sx={{ mt: 3 }}>
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="h6">External Dependencies</Typography>
        </Box>
        
        <List>
          <ListItem>
            <ListItemIcon>
              <StorageIcon />
            </ListItemIcon>
            <ListItemText
              primary="Supabase Database"
              secondary="Primary database for leads, forms, and responses"
            />
            <ListItemSecondaryAction>
              <Chip label="Connected" color="success" size="small" />
            </ListItemSecondaryAction>
          </ListItem>
          
          <ListItem>
            <ListItemIcon>
              <CloudIcon />
            </ListItemIcon>
            <ListItemText
              primary="OpenAI API"
              secondary="AI services for form analysis and outreach generation"
            />
            <ListItemSecondaryAction>
              <Chip label="Connected" color="success" size="small" />
            </ListItemSecondaryAction>
          </ListItem>
        </List>
      </Paper>
    </Box>
  );
};

export default SystemStatusPanel;

#!/bin/bash

echo "Starting Lead Generation System Services..."

# Set environment variables
export PYTHONPATH=$(pwd)

# Function to start a service in the background
start_service() {
    local service_name=$1
    local service_dir=$2
    local port=$3
    
    echo "Starting $service_name (Port $port)..."
    cd "$service_dir"
    python main.py &
    local pid=$!
    echo "$service_name started with PID $pid"
    cd - > /dev/null
}

# Start all services
start_service "Form Discovery Service" "services/form-discovery" "8001"
start_service "Response Monitoring Service" "services/response-ingestion" "8002"
start_service "Lead Management Service" "services/lead-management" "8003"
start_service "Analytics Service" "services/analytics" "8004"
start_service "AI Orchestrator Service" "services/ai-orchestrator" "8006"
start_service "Outreach Agent Service" "services/outreach-agent" "8007"
start_service "Lead Audit Orchestrator Service" "services/lead-audit-orchestrator" "8008"
start_service "API Gateway" "gateway" "8000"

echo ""
echo "All services started!"
echo ""
echo "Service URLs:"
echo "- API Gateway: http://localhost:8000"
echo "- Form Discovery: http://localhost:8001"
echo "- Response Monitoring: http://localhost:8002"
echo "- Lead Management: http://localhost:8003"
echo "- Analytics: http://localhost:8004"
echo "- AI Orchestrator: http://localhost:8006"
echo "- Outreach Agent: http://localhost:8007"
echo "- Lead Audit Orchestrator: http://localhost:8008"
echo ""
echo "Frontend: http://localhost:5173"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for all background processes
wait

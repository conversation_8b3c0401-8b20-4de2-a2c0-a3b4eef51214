@echo off
echo Starting Lead Generation System Services...

REM Set environment variables
set PYTHONPATH=%CD%

REM Start services in separate command windows
echo Starting Form Discovery Service (Port 8001)...
start "Form Discovery" cmd /k "cd services\form-discovery && python main.py"

echo Starting Response Monitoring Service (Port 8002)...
start "Response Monitoring" cmd /k "cd services\response-ingestion && python main.py"

echo Starting Lead Management Service (Port 8003)...
start "Lead Management" cmd /k "cd services\lead-management && python main.py"

echo Starting Analytics Service (Port 8004)...
start "Analytics" cmd /k "cd services\analytics && python main.py"

echo Starting AI Orchestrator Service (Port 8006)...
start "AI Orchestrator" cmd /k "cd services\ai-orchestrator && python main.py"

echo Starting Outreach Agent Service (Port 8007)...
start "Outreach Agent" cmd /k "cd services\outreach-agent && python main.py"

echo Starting Lead Audit Orchestrator Service (Port 8008)...
start "Lead Audit Orchestrator" cmd /k "cd services\lead-audit-orchestrator && python main.py"

echo Starting API Gateway (Port 8000)...
start "API Gateway" cmd /k "cd gateway && python main.py"

echo All services started! Check individual windows for status.
echo.
echo Service URLs:
echo - API Gateway: http://localhost:8000
echo - Form Discovery: http://localhost:8001
echo - Response Monitoring: http://localhost:8002
echo - Lead Management: http://localhost:8003
echo - Analytics: http://localhost:8004
echo - AI Orchestrator: http://localhost:8006
echo - Outreach Agent: http://localhost:8007
echo - Lead Audit Orchestrator: http://localhost:8008
echo.
echo Frontend: http://localhost:5173
echo.
pause

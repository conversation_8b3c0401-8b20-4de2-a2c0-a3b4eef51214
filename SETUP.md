# Lead Generation System - Setup Guide

## 🚀 Quick Start

### Prerequisites

1. **Node.js** (v18 or higher)
2. **Python** (v3.11 or higher)
3. **Git**
4. **Supabase Account** (for database)
5. **OpenAI API Key**

### Environment Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd lead-generation-system
   ```

2. **Copy environment files**
   ```bash
   # Backend environment
   cp .env.example .env
   
   # Frontend environment
   cp frontend/.env.example frontend/.env
   ```

3. **Configure environment variables**
   
   Edit `.env` and `frontend/.env` with your actual values:
   - Supabase URL and keys
   - OpenAI API key
   - Email credentials (for response monitoring)
   - SMS credentials (optional)

### Database Setup

1. **Create Supabase project** at https://supabase.com
2. **Run database migrations**
   ```bash
   # Execute the SQL scripts in scripts/ directory in your Supabase SQL editor
   psql -h <supabase-host> -U postgres -d postgres -f scripts/create_tables.sql
   ```

### Installation

#### Option 1: Local Development (Recommended)

1. **Install frontend dependencies**
   ```bash
   cd frontend
   npm install
   ```

2. **Install backend dependencies**
   ```bash
   # Install for each service
   cd services/form-discovery && pip install -r requirements.txt
   cd ../response-ingestion && pip install -r requirements.txt
   cd ../lead-management && pip install -r requirements.txt
   cd ../analytics && pip install -r requirements.txt
   cd ../ai-orchestrator && pip install -r requirements.txt
   cd ../outreach-agent && pip install -r requirements.txt
   cd ../lead-audit-orchestrator && pip install -r requirements.txt
   cd ../../gateway && pip install -r requirements.txt
   ```

3. **Start all services**
   ```bash
   # Windows
   scripts/start-all-services.bat
   
   # Linux/Mac
   chmod +x scripts/start-all-services.sh
   ./scripts/start-all-services.sh
   ```

4. **Start frontend**
   ```bash
   cd frontend
   npm run dev
   ```

#### Option 2: Docker (Production)

1. **Build and start with Docker Compose**
   ```bash
   docker-compose up --build
   ```

### Service URLs

Once running, the services will be available at:

- **Frontend**: http://localhost:5173
- **API Gateway**: http://localhost:8000
- **Form Discovery**: http://localhost:8001
- **Response Monitoring**: http://localhost:8002
- **Lead Management**: http://localhost:8003
- **Analytics**: http://localhost:8004
- **AI Orchestrator**: http://localhost:8006
- **Outreach Agent**: http://localhost:8007
- **Lead Audit Orchestrator**: http://localhost:8008

## 📋 System Features

### Core Functionality

1. **Lead Import**
   - CSV import with smart field mapping
   - Single company import with enrichment
   - Deduplication and validation

2. **Form Discovery & Submission**
   - AI-powered form detection
   - Automated form filling
   - Bulk submission capabilities

3. **Response Monitoring**
   - Email response tracking
   - SMS response monitoring
   - 48-hour monitoring periods

4. **Lead Audit System**
   - Automated audit orchestration
   - Response time scoring
   - Facebook pixel detection
   - Comprehensive audit reports

5. **Outreach Generation**
   - AI-powered email sequences (5 emails)
   - LinkedIn message sequences (3 messages)
   - Audit-based personalization

6. **Analytics & Reporting**
   - Real-time dashboards
   - Performance metrics
   - Industry breakdowns
   - Response time analysis

### Advanced Features

- **Lead Qualification**: Automatic disqualification of fast responders (<10 minutes)
- **Facebook Pixel Auditing**: Detection and analysis of tracking pixels
- **Multi-channel Response Tracking**: Email, SMS, and phone response monitoring
- **AI-Powered Insights**: GPT-4 powered analysis and recommendations

## 🔧 Configuration

### Service Configuration

Each service can be configured via environment variables:

```bash
# Core Services
FORM_DISCOVERY_URL=http://localhost:8001
RESPONSE_MONITORING_URL=http://localhost:8002
LEAD_MANAGEMENT_URL=http://localhost:8003
ANALYTICS_URL=http://localhost:8004

# AI Configuration
OPENAI_API_KEY=your_openai_api_key

# Database Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key

# Email Configuration
EMAIL_IMAP_SERVER=imap.gmail.com
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password
```

### Feature Flags

```bash
ENABLE_AI_REASONING=true
ENABLE_MOCK_DATA=false
ENABLE_AUDIT_LOGGING=true
```

## 🐛 Troubleshooting

### Common Issues

1. **Port conflicts**
   - Check if ports 8000-8008 are available
   - Modify port numbers in docker-compose.yml if needed

2. **Database connection issues**
   - Verify Supabase credentials
   - Check network connectivity
   - Ensure database tables are created

3. **OpenAI API errors**
   - Verify API key is valid
   - Check API quota and billing
   - Monitor rate limits

4. **Service startup failures**
   - Check logs in individual service windows
   - Verify all dependencies are installed
   - Ensure environment variables are set

### Health Checks

Each service provides a health check endpoint:
- GET /health

Example:
```bash
curl http://localhost:8001/health
```

### Logs

Service logs are available in:
- Individual service terminal windows (local development)
- Docker container logs: `docker-compose logs <service-name>`

## 📚 API Documentation

### Core Endpoints

- **Form Discovery**: POST /api/v1/discover
- **Form Submission**: POST /api/v1/submit
- **Lead Management**: GET/POST /api/v1/leads
- **Analytics**: GET /api/v1/metrics
- **Outreach Generation**: POST /generate-outreach

### Authentication

The system uses JWT tokens for authentication. Obtain a token via:
```bash
POST /auth/token
{
  "username": "admin",
  "password": "password"
}
```

## 🔒 Security

### Best Practices

1. **Environment Variables**: Never commit sensitive data to version control
2. **API Keys**: Rotate keys regularly
3. **Database Access**: Use service role keys for backend, anon keys for frontend
4. **CORS**: Configure appropriate origins for production
5. **Rate Limiting**: Enable rate limiting in production

### Production Deployment

1. **Use HTTPS**: Configure SSL certificates
2. **Environment Separation**: Use different databases for dev/staging/prod
3. **Monitoring**: Set up logging and monitoring
4. **Backups**: Configure automated database backups
5. **Scaling**: Use load balancers and container orchestration

## 📞 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review service logs for error details
3. Verify environment configuration
4. Test individual service health endpoints

## 🚀 Next Steps

After setup:
1. Import your first leads via CSV
2. Configure form submission settings
3. Set up response monitoring
4. Generate your first audit reports
5. Create outreach campaigns

The system is now ready for lead generation and response auditing!

#!/usr/bin/env python3
"""
Setup script for Email Monitor
Helps configure email settings and install dependencies
"""

import os
import sys
import subprocess

def install_dependencies():
    """Install required Python packages"""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements_email.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def check_env_file():
    """Check if .env file has email configuration"""
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        return False
    
    with open('.env', 'r') as f:
        content = f.read()
    
    required_vars = ['EMAIL_USERNAME', 'EMAIL_PASSWORD', 'EMAIL_IMAP_SERVER']
    missing_vars = []
    
    for var in required_vars:
        if var not in content or f"{var}=your_" in content:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  Missing or incomplete email configuration: {', '.join(missing_vars)}")
        print("\nPlease update your .env file with:")
        print("EMAIL_USERNAME=<EMAIL>")
        print("EMAIL_PASSWORD=your_actual_app_password")
        print("\n📝 Note: For Gmail, you need to:")
        print("1. Enable 2-factor authentication")
        print("2. Generate an App Password (not your regular password)")
        print("3. Use the App Password in EMAIL_PASSWORD")
        return False
    
    print("✅ Email configuration found in .env file")
    return True

def main():
    print("🚀 Email Monitor Setup")
    print("=" * 30)
    
    # Install dependencies
    if not install_dependencies():
        return
    
    # Check environment configuration
    if not check_env_file():
        print("\n❌ Setup incomplete. Please configure your .env file first.")
        return
    
    print("\n✅ Setup complete!")
    print("\n🎯 To start monitoring emails, run:")
    print("   python email_monitor.py")
    print("\n📊 To use a custom check interval (in seconds), run:")
    print("   python email_monitor.py 10")
    print("\n🛑 Press Ctrl+C to stop monitoring")

if __name__ == "__main__":
    main()

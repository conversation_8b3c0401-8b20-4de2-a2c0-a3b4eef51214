#!/usr/bin/env python3
"""
Email Monitor Script
Monitors and displays incoming emails in real-time using IMAP
"""

import imaplib
import email
import time
import os
import sys
from datetime import datetime
from email.header import decode_header
from dotenv import load_dotenv
import ssl

# Load environment variables
load_dotenv()

class EmailMonitor:
    def __init__(self):
        self.imap_server = os.getenv('EMAIL_IMAP_SERVER', 'imap.gmail.com')
        self.imap_port = int(os.getenv('EMAIL_IMAP_PORT', 993))
        self.username = os.getenv('EMAIL_USERNAME', '<EMAIL>')
        self.password = os.getenv('EMAIL_APP_PASSWORD', 'lium engf wbft gihk')
        self.mail = None
        self.last_uid = None
        
        if not self.username or not self.password:
            print("❌ Error: EMAIL_USERNAME and EMAIL_APP_PASSWORD must be set in .env file")
            sys.exit(1)
    
    def connect(self):
        """Connect to the IMAP server"""
        try:
            print(f"🔌 Connecting to {self.imap_server}:{self.imap_port}...")
            
            # Create SSL context
            context = ssl.create_default_context()
            
            # Connect to server
            self.mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port, ssl_context=context)
            
            # Login
            self.mail.login(self.username, self.password)
            print(f"✅ Successfully connected as {self.username}")
            
            # Select inbox
            self.mail.select('INBOX')
            print("📬 Monitoring INBOX for new emails...")
            
            return True
            
        except Exception as e:
            print(f"❌ Connection failed: {str(e)}")
            return False
    
    def decode_mime_words(self, s):
        """Decode MIME encoded words"""
        if s is None:
            return ""
        
        decoded_parts = []
        for part, encoding in decode_header(s):
            if isinstance(part, bytes):
                if encoding:
                    try:
                        decoded_parts.append(part.decode(encoding))
                    except:
                        decoded_parts.append(part.decode('utf-8', errors='ignore'))
                else:
                    decoded_parts.append(part.decode('utf-8', errors='ignore'))
            else:
                decoded_parts.append(str(part))
        
        return ''.join(decoded_parts)
    
    def get_email_body(self, msg):
        """Extract email body from message"""
        body = ""
        
        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))
                
                if content_type == "text/plain" and "attachment" not in content_disposition:
                    try:
                        body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                        break
                    except:
                        continue
                elif content_type == "text/html" and "attachment" not in content_disposition and not body:
                    try:
                        body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    except:
                        continue
        else:
            try:
                body = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
            except:
                body = str(msg.get_payload())
        
        return body.strip()
    
    def format_email(self, msg, uid):
        """Format email for display"""
        # Get email headers
        subject = self.decode_mime_words(msg.get("Subject", "No Subject"))
        from_addr = self.decode_mime_words(msg.get("From", "Unknown Sender"))
        to_addr = self.decode_mime_words(msg.get("To", "Unknown Recipient"))
        date = msg.get("Date", "Unknown Date")
        
        # Get email body
        body = self.get_email_body(msg)
        
        # Format output
        separator = "=" * 80
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        output = f"""
{separator}
📧 NEW EMAIL RECEIVED [{timestamp}] - UID: {uid}
{separator}
📤 From: {from_addr}
📥 To: {to_addr}
📅 Date: {date}
📋 Subject: {subject}
{separator}
📄 BODY:
{body[:1000]}{'...' if len(body) > 1000 else ''}
{separator}
"""
        return output
    
    def get_latest_emails(self):
        """Get latest emails since last check"""
        try:
            # Search for all emails
            status, messages = self.mail.search(None, 'ALL')
            
            if status != 'OK':
                return []
            
            # Get message UIDs
            message_ids = messages[0].split()
            
            if not message_ids:
                return []
            
            # Get the latest UID
            latest_uid = int(message_ids[-1])
            
            # If this is the first run, just store the latest UID and return
            if self.last_uid is None:
                self.last_uid = latest_uid
                print(f"📊 Found {len(message_ids)} existing emails. Monitoring for new ones...")
                return []
            
            # Check for new emails
            new_emails = []
            if latest_uid > self.last_uid:
                # Get new email UIDs
                new_uids = [uid for uid in message_ids if int(uid) > self.last_uid]
                
                for uid in new_uids:
                    try:
                        # Fetch email
                        status, msg_data = self.mail.fetch(uid, '(RFC822)')
                        
                        if status == 'OK':
                            # Parse email
                            email_body = msg_data[0][1]
                            msg = email.message_from_bytes(email_body)
                            new_emails.append((msg, uid.decode()))
                    
                    except Exception as e:
                        print(f"❌ Error fetching email UID {uid}: {str(e)}")
                
                # Update last UID
                self.last_uid = latest_uid
            
            return new_emails
            
        except Exception as e:
            print(f"❌ Error checking for emails: {str(e)}")
            return []
    
    def monitor(self, check_interval=30):
        """Monitor for new emails"""
        if not self.connect():
            return
        
        print(f"🔄 Checking for new emails every {check_interval} seconds...")
        print("Press Ctrl+C to stop monitoring\n")
        
        try:
            while True:
                # Check for new emails
                new_emails = self.get_latest_emails()
                
                # Display new emails
                for msg, uid in new_emails:
                    print(self.format_email(msg, uid))
                
                # Wait before next check
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
        except Exception as e:
            print(f"❌ Monitoring error: {str(e)}")
        finally:
            if self.mail:
                try:
                    self.mail.close()
                    self.mail.logout()
                    print("🔌 Disconnected from email server")
                except:
                    pass

def main():
    """Main function"""
    print("📧 Email Monitor Starting...")
    print("=" * 50)
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("⚠️  Warning: .env file not found. Please create one based on .env.example")
        print("Make sure to set EMAIL_USERNAME and EMAIL_PASSWORD")
        return
    
    # Create and start monitor
    monitor = EmailMonitor()
    
    # Allow custom check interval
    check_interval = 30
    if len(sys.argv) > 1:
        try:
            check_interval = int(sys.argv[1])
            print(f"📊 Using custom check interval: {check_interval} seconds")
        except ValueError:
            print("⚠️  Invalid interval, using default 30 seconds")
    
    monitor.monitor(check_interval)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Interactive Email Viewer
Lists recent emails and allows navigation with arrow keys
"""

import imaplib
import email
import os
import sys
import ssl
from datetime import datetime
from email.header import decode_header
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    import keyboard
    import colorama
    from colorama import Fore, Back, Style
    colorama.init()
    KEYBOARD_AVAILABLE = True
except ImportError:
    KEYBOARD_AVAILABLE = False
    print("⚠️  For better experience, install: pip install keyboard colorama")

class EmailViewer:
    def __init__(self):
        self.imap_server = os.getenv('EMAIL_IMAP_SERVER', 'imap.gmail.com')
        self.imap_port = int(os.getenv('EMAIL_IMAP_PORT', 993))
        self.username = os.getenv('EMAIL_USERNAME', '<EMAIL>')
        self.password = os.getenv('EMAIL_APP_PASSWORD', 'lium engf wbft gihk')
        self.mail = None
        self.emails = []
        self.selected_index = 0
        
        if not self.username or not self.password:
            print("❌ Error: EMAIL_USERNAME and EMAIL_APP_PASSWORD must be set")
            sys.exit(1)
    
    def connect(self):
        """Connect to the IMAP server"""
        try:
            print(f"🔌 Connecting to {self.imap_server}:{self.imap_port}...")
            
            # Create SSL context
            context = ssl.create_default_context()
            
            # Connect to server
            self.mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port, ssl_context=context)
            
            # Login
            self.mail.login(self.username, self.password)
            print(f"✅ Successfully connected as {self.username}")
            
            # Select inbox
            self.mail.select('INBOX')
            
            return True
            
        except Exception as e:
            print(f"❌ Connection failed: {str(e)}")
            return False
    
    def decode_mime_words(self, s):
        """Decode MIME encoded words"""
        if s is None:
            return ""
        
        decoded_parts = []
        for part, encoding in decode_header(s):
            if isinstance(part, bytes):
                if encoding:
                    try:
                        decoded_parts.append(part.decode(encoding))
                    except:
                        decoded_parts.append(part.decode('utf-8', errors='ignore'))
                else:
                    decoded_parts.append(part.decode('utf-8', errors='ignore'))
            else:
                decoded_parts.append(str(part))
        
        return ''.join(decoded_parts)
    
    def get_email_body(self, msg):
        """Extract email body from message"""
        body = ""
        
        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))
                
                if content_type == "text/plain" and "attachment" not in content_disposition:
                    try:
                        body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                        break
                    except:
                        continue
                elif content_type == "text/html" and "attachment" not in content_disposition and not body:
                    try:
                        body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    except:
                        continue
        else:
            try:
                body = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
            except:
                body = str(msg.get_payload())
        
        return body.strip()
    
    def fetch_recent_emails(self, count=10):
        """Fetch the most recent emails"""
        try:
            # Search for all emails
            status, messages = self.mail.search(None, 'ALL')
            
            if status != 'OK':
                print("❌ Failed to search emails")
                return False
            
            # Get message UIDs
            message_ids = messages[0].split()
            
            if not message_ids:
                print("📭 No emails found")
                return False
            
            # Get the last 'count' emails (most recent)
            recent_ids = message_ids[-count:]
            recent_ids.reverse()  # Most recent first
            
            print(f"📧 Fetching {len(recent_ids)} recent emails...")
            
            self.emails = []
            for i, uid in enumerate(recent_ids):
                try:
                    # Fetch email
                    status, msg_data = self.mail.fetch(uid, '(RFC822)')
                    
                    if status == 'OK':
                        # Parse email
                        email_body = msg_data[0][1]
                        msg = email.message_from_bytes(email_body)
                        
                        # Extract email info
                        subject = self.decode_mime_words(msg.get("Subject", "No Subject"))
                        from_addr = self.decode_mime_words(msg.get("From", "Unknown Sender"))
                        date = msg.get("Date", "Unknown Date")
                        body = self.get_email_body(msg)
                        
                        self.emails.append({
                            'uid': uid.decode(),
                            'subject': subject,
                            'from': from_addr,
                            'date': date,
                            'body': body,
                            'msg': msg
                        })
                        
                        print(f"  📄 {i+1}/{len(recent_ids)}: {subject[:50]}...")
                
                except Exception as e:
                    print(f"❌ Error fetching email UID {uid}: {str(e)}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error fetching emails: {str(e)}")
            return False
    
    def display_email_list(self):
        """Display the list of emails with selection"""
        if KEYBOARD_AVAILABLE:
            os.system('cls' if os.name == 'nt' else 'clear')
        
        print("📧 Recent Emails")
        print("=" * 80)
        
        for i, email_info in enumerate(self.emails):
            # Truncate long subjects and sender names
            subject = email_info['subject'][:60] + "..." if len(email_info['subject']) > 60 else email_info['subject']
            from_addr = email_info['from'][:30] + "..." if len(email_info['from']) > 30 else email_info['from']
            
            if KEYBOARD_AVAILABLE:
                if i == self.selected_index:
                    print(f"{Back.BLUE}{Fore.WHITE}► {i+1:2d}. {subject:<62} | {from_addr}{Style.RESET_ALL}")
                else:
                    print(f"  {i+1:2d}. {subject:<62} | {from_addr}")
            else:
                marker = "►" if i == self.selected_index else " "
                print(f"{marker} {i+1:2d}. {subject:<62} | {from_addr}")
        
        print("=" * 80)
        if KEYBOARD_AVAILABLE:
            print("🎮 Use ↑/↓ arrows to navigate, ENTER to open, 'q' to quit, 'r' to refresh")
        else:
            print("📝 Enter email number (1-10), 'r' to refresh, 'q' to quit:")
    
    def display_email_content(self, email_info):
        """Display the full content of selected email"""
        if KEYBOARD_AVAILABLE:
            os.system('cls' if os.name == 'nt' else 'clear')
        
        separator = "=" * 80
        print(separator)
        print(f"📧 EMAIL DETAILS")
        print(separator)
        print(f"📤 From: {email_info['from']}")
        print(f"📅 Date: {email_info['date']}")
        print(f"📋 Subject: {email_info['subject']}")
        print(f"🆔 UID: {email_info['uid']}")
        print(separator)
        print("📄 BODY:")
        print(separator)
        print(email_info['body'])
        print(separator)
        
        if KEYBOARD_AVAILABLE:
            print("Press any key to return to email list...")
            keyboard.read_event()
        else:
            input("Press Enter to return to email list...")
    
    def run_interactive_mode(self):
        """Run interactive mode with keyboard navigation"""
        if not KEYBOARD_AVAILABLE:
            return self.run_simple_mode()
        
        while True:
            self.display_email_list()
            
            try:
                event = keyboard.read_event()
                
                if event.event_type == keyboard.KEY_DOWN:
                    if event.name == 'up':
                        self.selected_index = max(0, self.selected_index - 1)
                    elif event.name == 'down':
                        self.selected_index = min(len(self.emails) - 1, self.selected_index + 1)
                    elif event.name == 'enter':
                        if self.emails:
                            self.display_email_content(self.emails[self.selected_index])
                    elif event.name == 'r':
                        print("🔄 Refreshing emails...")
                        self.fetch_recent_emails()
                        self.selected_index = 0
                    elif event.name == 'q':
                        break
                        
            except KeyboardInterrupt:
                break
    
    def run_simple_mode(self):
        """Run simple mode without keyboard library"""
        while True:
            self.display_email_list()
            
            try:
                choice = input().strip().lower()
                
                if choice == 'q':
                    break
                elif choice == 'r':
                    print("🔄 Refreshing emails...")
                    self.fetch_recent_emails()
                    self.selected_index = 0
                elif choice.isdigit():
                    index = int(choice) - 1
                    if 0 <= index < len(self.emails):
                        self.display_email_content(self.emails[index])
                    else:
                        print("❌ Invalid email number")
                        input("Press Enter to continue...")
                        
            except KeyboardInterrupt:
                break
    
    def run(self):
        """Main run method"""
        if not self.connect():
            return
        
        if not self.fetch_recent_emails():
            return
        
        try:
            if KEYBOARD_AVAILABLE:
                self.run_interactive_mode()
            else:
                self.run_simple_mode()
        finally:
            if self.mail:
                try:
                    self.mail.close()
                    self.mail.logout()
                    print("\n🔌 Disconnected from email server")
                except:
                    pass

def main():
    """Main function"""
    print("📧 Interactive Email Viewer")
    print("=" * 50)
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("⚠️  Warning: .env file not found. Please create one based on .env.example")
        return
    
    # Create and start viewer
    viewer = EmailViewer()
    viewer.run()

if __name__ == "__main__":
    main()

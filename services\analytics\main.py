"""
Analytics Service

Provides analytics and insights for the lead generation system.
"""

import os
import uuid
import datetime
from typing import Dict, List, Any, Optional
from fastapi import FastAP<PERSON>, HTTPException, status, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import logging

# Import shared libraries
import sys
sys.path.append("../../")
from libs.logging import get_service_logger

# Import analytics modules
from response_audit import ResponseAudit

# Configure logging
logger = get_service_logger("analytics-service")

# Initialize FastAPI app
app = FastAPI(
    title="Analytics Service",
    description="Analytics and insights for the lead generation system",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize analytics components
response_audit = ResponseAudit()

# Pydantic models
class MetricsResponse(BaseModel):
    total_leads: int
    forms_submitted: int
    response_rate: float
    avg_response_time: float
    conversion_rate: float
    facebook_pixel_detection_rate: float

class TrendData(BaseModel):
    date: str
    leads: int
    submissions: int
    responses: int

class ChannelPerformance(BaseModel):
    channel: str
    responses: int
    avg_response_time: str

class ResponseTimeDistribution(BaseModel):
    range: str
    count: int
    percentage: float

class IndustryBreakdown(BaseModel):
    industry: str
    lead_count: int
    response_rate: float

class AnalyticsData(BaseModel):
    metrics: MetricsResponse
    trends: List[TrendData]
    channel_performance: List[ChannelPerformance]
    response_time_distribution: List[ResponseTimeDistribution]
    industry_breakdown: List[IndustryBreakdown]

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "analytics"}

@app.get("/api/v1/metrics", response_model=MetricsResponse)
async def get_metrics(
    timeframe: str = Query("30d", description="Time range: 7d, 30d, 90d, 1y")
):
    """Get overview metrics for the dashboard."""
    try:
        # Calculate date range
        end_date = datetime.datetime.utcnow()
        if timeframe == "7d":
            start_date = end_date - datetime.timedelta(days=7)
        elif timeframe == "30d":
            start_date = end_date - datetime.timedelta(days=30)
        elif timeframe == "90d":
            start_date = end_date - datetime.timedelta(days=90)
        elif timeframe == "1y":
            start_date = end_date - datetime.timedelta(days=365)
        else:
            start_date = end_date - datetime.timedelta(days=30)

        # Fetch leads data
        leads = response_audit.fetch_leads(start_date=start_date, end_date=end_date, limit=10000)
        
        # Calculate metrics
        total_leads = len(leads)
        forms_submitted = len([lead for lead in leads if lead.get('website')])
        
        # Mock response data for now - in production this would come from responses table
        response_rate = 25.5  # percentage
        avg_response_time = 4.2  # hours
        conversion_rate = 12.8  # percentage
        
        # Calculate Facebook pixel detection rate
        leads_with_pixel = len([lead for lead in leads if lead.get('facebook_pixel_detected')])
        facebook_pixel_detection_rate = (leads_with_pixel / total_leads * 100) if total_leads > 0 else 0

        return MetricsResponse(
            total_leads=total_leads,
            forms_submitted=forms_submitted,
            response_rate=response_rate,
            avg_response_time=avg_response_time,
            conversion_rate=conversion_rate,
            facebook_pixel_detection_rate=facebook_pixel_detection_rate
        )

    except Exception as e:
        logger.error(f"Error getting metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting metrics: {str(e)}"
        )

@app.get("/api/v1/trends", response_model=List[TrendData])
async def get_trends(
    timeframe: str = Query("30d", description="Time range: 7d, 30d, 90d"),
    granularity: str = Query("daily", description="Granularity: daily, weekly")
):
    """Get trend data for charts."""
    try:
        # Calculate date range
        end_date = datetime.datetime.utcnow()
        if timeframe == "7d":
            start_date = end_date - datetime.timedelta(days=7)
            days = 7
        elif timeframe == "30d":
            start_date = end_date - datetime.timedelta(days=30)
            days = 30
        elif timeframe == "90d":
            start_date = end_date - datetime.timedelta(days=90)
            days = 90
        else:
            start_date = end_date - datetime.timedelta(days=30)
            days = 30

        # Generate trend data (mock for now)
        trends = []
        for i in range(days):
            date = (start_date + datetime.timedelta(days=i)).strftime("%Y-%m-%d")
            trends.append(TrendData(
                date=date,
                leads=max(0, int(20 + (i % 7) * 3 + (i % 3) * 2)),
                submissions=max(0, int(15 + (i % 5) * 2 + (i % 4) * 1)),
                responses=max(0, int(8 + (i % 6) * 1 + (i % 2) * 2))
            ))

        return trends

    except Exception as e:
        logger.error(f"Error getting trends: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting trends: {str(e)}"
        )

@app.get("/api/v1/channel-performance", response_model=List[ChannelPerformance])
async def get_channel_performance():
    """Get channel performance data."""
    try:
        # Mock channel performance data
        channels = [
            ChannelPerformance(channel="Email", responses=156, avg_response_time="4.2h"),
            ChannelPerformance(channel="Phone", responses=89, avg_response_time="12m"),
            ChannelPerformance(channel="SMS", responses=67, avg_response_time="1.8h")
        ]
        
        return channels

    except Exception as e:
        logger.error(f"Error getting channel performance: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting channel performance: {str(e)}"
        )

@app.get("/api/v1/response-time-distribution", response_model=List[ResponseTimeDistribution])
async def get_response_time_distribution():
    """Get response time distribution data."""
    try:
        # Mock response time distribution
        distribution = [
            ResponseTimeDistribution(range="0-10m", count=23, percentage=15.2),
            ResponseTimeDistribution(range="10m-1h", count=45, percentage=29.8),
            ResponseTimeDistribution(range="1h-4h", count=38, percentage=25.2),
            ResponseTimeDistribution(range="4h-24h", count=32, percentage=21.2),
            ResponseTimeDistribution(range="24h+", count=13, percentage=8.6)
        ]
        
        return distribution

    except Exception as e:
        logger.error(f"Error getting response time distribution: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting response time distribution: {str(e)}"
        )

@app.get("/api/v1/industry-breakdown", response_model=List[IndustryBreakdown])
async def get_industry_breakdown():
    """Get industry breakdown data."""
    try:
        # Mock industry breakdown
        industries = [
            IndustryBreakdown(industry="Roofing", lead_count=245, response_rate=28.5),
            IndustryBreakdown(industry="HVAC", lead_count=189, response_rate=32.1),
            IndustryBreakdown(industry="Plumbing", lead_count=156, response_rate=25.8),
            IndustryBreakdown(industry="Electrical", lead_count=134, response_rate=29.7),
            IndustryBreakdown(industry="General Contractor", lead_count=98, response_rate=22.4)
        ]
        
        return industries

    except Exception as e:
        logger.error(f"Error getting industry breakdown: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting industry breakdown: {str(e)}"
        )

@app.get("/api/v1/analytics", response_model=AnalyticsData)
async def get_all_analytics(
    timeframe: str = Query("30d", description="Time range: 7d, 30d, 90d, 1y")
):
    """Get all analytics data in one request."""
    try:
        # Get all analytics data
        metrics = await get_metrics(timeframe)
        trends = await get_trends(timeframe)
        channel_performance = await get_channel_performance()
        response_time_distribution = await get_response_time_distribution()
        industry_breakdown = await get_industry_breakdown()

        return AnalyticsData(
            metrics=metrics,
            trends=trends,
            channel_performance=channel_performance,
            response_time_distribution=response_time_distribution,
            industry_breakdown=industry_breakdown
        )

    except Exception as e:
        logger.error(f"Error getting all analytics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting all analytics: {str(e)}"
        )

@app.get("/api/v1/report")
async def generate_report(
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    format: str = Query("json", description="Report format: json, csv")
):
    """Generate a comprehensive analytics report."""
    try:
        # Parse dates if provided
        start_dt = None
        end_dt = None
        
        if start_date:
            start_dt = datetime.datetime.strptime(start_date, "%Y-%m-%d")
        if end_date:
            end_dt = datetime.datetime.strptime(end_date, "%Y-%m-%d")

        # Generate report using response audit
        report = response_audit.generate_response_time_report(
            start_date=start_dt,
            end_date=end_dt
        )

        if format == "csv":
            # Convert to CSV format
            import csv
            import io
            
            output = io.StringIO()
            if report:
                writer = csv.DictWriter(output, fieldnames=report[0].keys())
                writer.writeheader()
                writer.writerows(report)
            
            return {"report": output.getvalue(), "format": "csv"}
        else:
            return {"report": report, "format": "json"}

    except Exception as e:
        logger.error(f"Error generating report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating report: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8004)

import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Speed as SpeedIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Sms as SmsIcon,
  Facebook as FacebookIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import PageHeader from '../components/common/PageHeader';
import supabaseService from '../services/supabaseService';

/**
 * Analytics Page Component
 * 
 * Provides comprehensive analytics and insights for the lead generation system
 */
const AnalyticsPage = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [timeRange, setTimeRange] = useState('30d');
  
  // Analytics data state
  const [analytics, setAnalytics] = useState({
    overview: {
      totalLeads: 0,
      formsSubmitted: 0,
      responseRate: 0,
      avgResponseTime: 0,
      conversionRate: 0
    },
    trends: [],
    channelPerformance: [],
    responseTimeDistribution: [],
    industryBreakdown: [],
    facebookPixelStats: {
      detected: 0,
      missing: 0,
      percentage: 0
    }
  });

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch leads data
      const { leads } = await supabaseService.getLeads();
      
      // Calculate overview metrics
      const totalLeads = leads.length;
      const leadsWithWebsites = leads.filter(lead => lead.website).length;
      const leadsWithFacebookPixel = leads.filter(lead => lead.facebook_pixel_detected).length;
      
      // Mock response data for now - in production this would come from responses table
      const mockResponseRate = Math.round(Math.random() * 30 + 15); // 15-45%
      const mockAvgResponseTime = Math.round(Math.random() * 8 + 2); // 2-10 hours
      const mockConversionRate = Math.round(Math.random() * 15 + 5); // 5-20%

      // Generate trend data (last 30 days)
      const trendData = Array.from({ length: 30 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (29 - i));
        return {
          date: date.toISOString().split('T')[0],
          leads: Math.round(Math.random() * 20 + 5),
          submissions: Math.round(Math.random() * 15 + 3),
          responses: Math.round(Math.random() * 8 + 1)
        };
      });

      // Channel performance data
      const channelData = [
        { channel: 'Email', responses: Math.round(Math.random() * 100 + 50), avgTime: '4.2h' },
        { channel: 'Phone', responses: Math.round(Math.random() * 80 + 30), avgTime: '12m' },
        { channel: 'SMS', responses: Math.round(Math.random() * 60 + 20), avgTime: '1.8h' }
      ];

      // Response time distribution
      const responseTimeData = [
        { range: '0-10m', count: Math.round(Math.random() * 20 + 5), color: '#4caf50' },
        { range: '10m-1h', count: Math.round(Math.random() * 30 + 10), color: '#8bc34a' },
        { range: '1h-4h', count: Math.round(Math.random() * 40 + 15), color: '#ffc107' },
        { range: '4h-24h', count: Math.round(Math.random() * 35 + 20), color: '#ff9800' },
        { range: '24h+', count: Math.round(Math.random() * 25 + 10), color: '#f44336' }
      ];

      // Industry breakdown
      const industries = ['Roofing', 'HVAC', 'Plumbing', 'Electrical', 'General Contractor'];
      const industryData = industries.map(industry => ({
        industry,
        count: Math.round(Math.random() * 100 + 20),
        responseRate: Math.round(Math.random() * 40 + 10)
      }));

      setAnalytics({
        overview: {
          totalLeads,
          formsSubmitted: leadsWithWebsites,
          responseRate: mockResponseRate,
          avgResponseTime: mockAvgResponseTime,
          conversionRate: mockConversionRate
        },
        trends: trendData,
        channelPerformance: channelData,
        responseTimeDistribution: responseTimeData,
        industryBreakdown: industryData,
        facebookPixelStats: {
          detected: leadsWithFacebookPixel,
          missing: totalLeads - leadsWithFacebookPixel,
          percentage: totalLeads > 0 ? Math.round((leadsWithFacebookPixel / totalLeads) * 100) : 0
        }
      });

    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleTimeRangeChange = (event) => {
    setTimeRange(event.target.value);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box>
      <PageHeader
        title="Analytics"
        description="Comprehensive insights and analytics for your lead generation system"
        breadcrumbs={[
          { label: 'Analytics', icon: <AnalyticsIcon fontSize="inherit" /> }
        ]}
      />

      {/* Time Range Selector */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Time Range</InputLabel>
          <Select
            value={timeRange}
            label="Time Range"
            onChange={handleTimeRangeChange}
          >
            <MenuItem value="7d">Last 7 days</MenuItem>
            <MenuItem value="30d">Last 30 days</MenuItem>
            <MenuItem value="90d">Last 90 days</MenuItem>
            <MenuItem value="1y">Last year</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <TrendingUpIcon sx={{ color: 'primary.main', mr: 1 }} />
                <Typography variant="h6" component="div">
                  {analytics.overview.totalLeads}
                </Typography>
              </Box>
              <Typography color="text.secondary" variant="body2">
                Total Leads
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <AssessmentIcon sx={{ color: 'success.main', mr: 1 }} />
                <Typography variant="h6" component="div">
                  {analytics.overview.formsSubmitted}
                </Typography>
              </Box>
              <Typography color="text.secondary" variant="body2">
                Forms Submitted
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <SpeedIcon sx={{ color: 'info.main', mr: 1 }} />
                <Typography variant="h6" component="div">
                  {analytics.overview.responseRate}%
                </Typography>
              </Box>
              <Typography color="text.secondary" variant="body2">
                Response Rate
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <SpeedIcon sx={{ color: 'warning.main', mr: 1 }} />
                <Typography variant="h6" component="div">
                  {analytics.overview.avgResponseTime}h
                </Typography>
              </Box>
              <Typography color="text.secondary" variant="body2">
                Avg Response Time
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <TrendingUpIcon sx={{ color: 'success.main', mr: 1 }} />
                <Typography variant="h6" component="div">
                  {analytics.overview.conversionRate}%
                </Typography>
              </Box>
              <Typography color="text.secondary" variant="body2">
                Conversion Rate
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs for different analytics views */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="Trends" />
          <Tab label="Channel Performance" />
          <Tab label="Response Times" />
          <Tab label="Industry Analysis" />
          <Tab label="Facebook Pixel" />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {/* Trends Tab */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Lead Generation Trends
              </Typography>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={analytics.trends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="leads" stroke="#1976d2" strokeWidth={2} />
                  <Line type="monotone" dataKey="submissions" stroke="#4caf50" strokeWidth={2} />
                  <Line type="monotone" dataKey="responses" stroke="#ff9800" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </Box>
          )}

          {/* Channel Performance Tab */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Response Channel Performance
              </Typography>
              <Grid container spacing={3}>
                {analytics.channelPerformance.map((channel, index) => (
                  <Grid item xs={12} md={4} key={channel.channel}>
                    <Card>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          {channel.channel === 'Email' && <EmailIcon sx={{ mr: 1, color: 'primary.main' }} />}
                          {channel.channel === 'Phone' && <PhoneIcon sx={{ mr: 1, color: 'success.main' }} />}
                          {channel.channel === 'SMS' && <SmsIcon sx={{ mr: 1, color: 'info.main' }} />}
                          <Typography variant="h6">{channel.channel}</Typography>
                        </Box>
                        <Typography variant="h4" color="primary" gutterBottom>
                          {channel.responses}
                        </Typography>
                        <Typography color="text.secondary">
                          Responses
                        </Typography>
                        <Chip 
                          label={`Avg: ${channel.avgTime}`} 
                          size="small" 
                          sx={{ mt: 1 }}
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* Response Times Tab */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Response Time Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={analytics.responseTimeDistribution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="range" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#1976d2" />
                </BarChart>
              </ResponsiveContainer>
            </Box>
          )}

          {/* Industry Analysis Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Industry Breakdown
              </Typography>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={analytics.industryBreakdown}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="industry" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="count" fill="#1976d2" name="Lead Count" />
                  <Bar dataKey="responseRate" fill="#4caf50" name="Response Rate %" />
                </BarChart>
              </ResponsiveContainer>
            </Box>
          )}

          {/* Facebook Pixel Tab */}
          {activeTab === 4 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Facebook Pixel Analysis
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <FacebookIcon sx={{ mr: 1, color: '#1877f2' }} />
                        <Typography variant="h6">Pixel Detection Rate</Typography>
                      </Box>
                      <Typography variant="h3" color="primary" gutterBottom>
                        {analytics.facebookPixelStats.percentage}%
                      </Typography>
                      <Typography color="text.secondary">
                        {analytics.facebookPixelStats.detected} of {analytics.facebookPixelStats.detected + analytics.facebookPixelStats.missing} websites have Facebook Pixel
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={[
                          { name: 'Pixel Detected', value: analytics.facebookPixelStats.detected, fill: '#4caf50' },
                          { name: 'Pixel Missing', value: analytics.facebookPixelStats.missing, fill: '#f44336' }
                        ]}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        <Cell fill="#4caf50" />
                        <Cell fill="#f44336" />
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </Grid>
              </Grid>
            </Box>
          )}
        </Box>
      </Paper>
    </Box>
  );
};

export default AnalyticsPage;

import React, { useState } from 'react';
import { BrowserRouter, Routes, Route, Link, Navigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Box,
  CssBaseline,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Divider,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  useTheme,
  useMediaQuery,
  Tooltip,
  Container,
  Breadcrumbs
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  CloudUpload as CloudUploadIcon,
  Search as SearchIcon,
  Send as SendIcon,
  Notifications as NotificationsIcon,
  Assessment as AssessmentIcon,
  Settings as SettingsIcon,
  BarChart as AnalyticsIcon,
  AccountCircle as AccountCircleIcon,
  ChevronRight as ChevronRightIcon,
  Logout as LogoutIcon,
  Help as HelpIcon,
  Notifications as NotificationIcon,
  People as PeopleIcon,
  AutoMode as AutoModeIcon
} from '@mui/icons-material';

// Import pages
import SettingsPage from './pages/SettingsPage';
import LeadImportPage from './pages/LeadImportPage';
import AuditReportBalanced from './components/AuditReportBalanced';
import ResponseMonitoring from './components/ResponseMonitoring';
import NavLink from './components/common/NavLink';
import PageHeader from './components/common/PageHeader';
import TestComponent from './components/common/TestComponent';
import LeadManagementPage from './pages/LeadManagementPage';
import FormDiscoveryAndSubmissionPage from './pages/FormDiscoveryAndSubmissionPage';
import LeadAuditOrchestratorPage from './pages/LeadAuditOrchestratorPage';
import AnalyticsPage from './pages/AnalyticsPage';

// Dashboard component
const Dashboard = () => (
  <Box>
    <PageHeader
      title="Dashboard"
      description="Welcome to the Lead Generation System Dashboard"
    />

    {/* Quick Stats */}
    <Box sx={{ mb: 6 }}>
      <Typography variant="h6" gutterBottom fontWeight="medium">
        Quick Stats
      </Typography>
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(240px, 1fr))',
        gap: 3
      }}>
        {/* Total Forms Card */}
        <Box
          sx={{
            p: 3,
            bgcolor: 'background.paper',
            borderRadius: 2,
            boxShadow: 1,
            border: '1px solid',
            borderColor: 'primary.light',
            position: 'relative',
            overflow: 'hidden',
            transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
            '&:hover': {
              transform: 'translateY(-4px)',
              boxShadow: '0 6px 12px rgba(0, 0, 0, 0.1)',
            }
          }}
          className="slide-in"
        >
          <Box sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '4px',
            height: '100%',
            bgcolor: 'primary.main'
          }} />
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Total Forms
          </Typography>
          <Typography variant="h4" fontWeight="bold">
            24
          </Typography>
        </Box>

        {/* Active Leads Card */}
        <Box
          sx={{
            p: 3,
            bgcolor: 'background.paper',
            borderRadius: 2,
            boxShadow: 1,
            border: '1px solid',
            borderColor: 'success.light',
            position: 'relative',
            overflow: 'hidden',
            transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
            '&:hover': {
              transform: 'translateY(-4px)',
              boxShadow: '0 6px 12px rgba(0, 0, 0, 0.1)',
            }
          }}
          className="slide-in"
          style={{ animationDelay: '0.1s' }}
        >
          <Box sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '4px',
            height: '100%',
            bgcolor: 'success.main'
          }} />
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Active Leads
          </Typography>
          <Typography variant="h4" fontWeight="bold">
            156
          </Typography>
        </Box>

        {/* Responses Card */}
        <Box
          sx={{
            p: 3,
            bgcolor: 'background.paper',
            borderRadius: 2,
            boxShadow: 1,
            border: '1px solid',
            borderColor: 'warning.light',
            position: 'relative',
            overflow: 'hidden',
            transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
            '&:hover': {
              transform: 'translateY(-4px)',
              boxShadow: '0 6px 12px rgba(0, 0, 0, 0.1)',
            }
          }}
          className="slide-in"
          style={{ animationDelay: '0.2s' }}
        >
          <Box sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '4px',
            height: '100%',
            bgcolor: 'warning.main'
          }} />
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Responses
          </Typography>
          <Typography variant="h4" fontWeight="bold">
            89
          </Typography>
        </Box>

        {/* Conversion Rate Card */}
        <Box
          sx={{
            p: 3,
            bgcolor: 'background.paper',
            borderRadius: 2,
            boxShadow: 1,
            border: '1px solid',
            borderColor: 'info.light',
            position: 'relative',
            overflow: 'hidden',
            transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
            '&:hover': {
              transform: 'translateY(-4px)',
              boxShadow: '0 6px 12px rgba(0, 0, 0, 0.1)',
            }
          }}
          className="slide-in"
          style={{ animationDelay: '0.3s' }}
        >
          <Box sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '4px',
            height: '100%',
            bgcolor: 'info.main'
          }} />
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Conversion Rate
          </Typography>
          <Typography variant="h4" fontWeight="bold">
            32%
          </Typography>
        </Box>
      </Box>
    </Box>
  </Box>
);



// Navigation items configuration
const navItems = [
  { text: 'Dashboard', icon: <DashboardIcon />, path: '/dashboard' },
  { text: 'Lead Import', icon: <CloudUploadIcon />, path: '/lead-import' },
  { text: 'Lead Management', icon: <PeopleIcon />, path: '/lead-management' },
  { text: 'Form Submitter', icon: <SearchIcon />, path: '/form-discovery-submission' },
  { text: 'Audit Orchestrator', icon: <AutoModeIcon />, path: '/audit-orchestrator' },
  { text: 'Response Monitoring', icon: <NotificationsIcon />, path: '/response-monitoring' },
  { text: 'Lead Response Audit', icon: <AssessmentIcon />, path: '/audit-report' },
  { text: 'Analytics', icon: <AnalyticsIcon />, path: '/analytics' },
  { text: 'Settings', icon: <SettingsIcon />, path: '/settings' },
  // Temporary test item - remove in production
  { text: 'UI Test', icon: <HelpIcon />, path: '/test' },
];

// Drawer width
const drawerWidth = 260;

// App component
function App() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [user] = useState({ fullName: 'Admin User', avatar: '' });
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    alert('Logout functionality would be implemented here');
    handleProfileMenuClose();
  };

  // Drawer content
  const drawer = (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Box sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Typography variant="h6" component="div" sx={{ fontWeight: 'bold', color: '#ffffff' }}>
          Lead Gen System
        </Typography>
      </Box>
      <Divider sx={{ bgcolor: 'rgba(255, 255, 255, 0.1)' }} />
      <List sx={{ flexGrow: 1, px: 1 }}>
        {navItems.map((item) => (
          <NavLink
            key={item.text}
            to={item.path}
            icon={item.icon}
            text={item.text}
            onClick={isMobile ? handleDrawerToggle : undefined}
          />
        ))}
      </List>
      <Divider sx={{ bgcolor: 'rgba(255, 255, 255, 0.1)' }} />
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.5)' }}>
          Lead Generation System v1.0
        </Typography>
      </Box>
    </Box>
  );

  return (
    <BrowserRouter>
      <Box sx={{ display: 'flex', minHeight: '100vh' }}>
        <CssBaseline />

        {/* App Bar */}
        <AppBar
          position="fixed"
          sx={{
            zIndex: (theme) => theme.zIndex.drawer + 1,
            bgcolor: 'background.paper',
            color: 'text.primary',
            boxShadow: 1
          }}
        >
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2, display: { md: 'none' } }}
            >
              <MenuIcon />
            </IconButton>

            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{ display: { xs: 'none', sm: 'block' }, fontWeight: 'bold', color: 'primary.main' }}
            >
              Lead Gen System
            </Typography>

            <Box sx={{ flexGrow: 1 }} />

            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip title="Notifications">
                <IconButton color="inherit" size="large">
                  <Badge badgeContent={4} color="error">
                    <NotificationIcon />
                  </Badge>
                </IconButton>
              </Tooltip>

              <Tooltip title="Help">
                <IconButton color="inherit" size="large">
                  <HelpIcon />
                </IconButton>
              </Tooltip>

              <Tooltip title="Account">
                <IconButton
                  size="large"
                  edge="end"
                  aria-label="account of current user"
                  aria-haspopup="true"
                  onClick={handleProfileMenuOpen}
                  color="inherit"
                >
                  {user.avatar ? (
                    <Avatar src={user.avatar} sx={{ width: 32, height: 32 }} />
                  ) : (
                    <AccountCircleIcon />
                  )}
                </IconButton>
              </Tooltip>
            </Box>
          </Toolbar>
        </AppBar>

        {/* Profile Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleProfileMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <Box sx={{ px: 2, py: 1 }}>
            <Typography variant="subtitle1" fontWeight="medium">
              {user.fullName}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Administrator
            </Typography>
          </Box>
          <Divider />
          <MenuItem onClick={handleProfileMenuClose}>
            <ListItemIcon>
              <AccountCircleIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>My Profile</ListItemText>
          </MenuItem>
          <MenuItem onClick={handleProfileMenuClose}>
            <ListItemIcon>
              <SettingsIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Account Settings</ListItemText>
          </MenuItem>
          <Divider />
          <MenuItem onClick={handleLogout}>
            <ListItemIcon>
              <LogoutIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Logout</ListItemText>
          </MenuItem>
        </Menu>

        {/* Drawer */}
        <Box
          component="nav"
          sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
        >
          {/* Mobile drawer */}
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{
              keepMounted: true, // Better open performance on mobile
            }}
            sx={{
              display: { xs: 'block', md: 'none' },
              '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
            }}
          >
            {drawer}
          </Drawer>

          {/* Desktop drawer */}
          <Drawer
            variant="permanent"
            sx={{
              display: { xs: 'none', md: 'block' },
              '& .MuiDrawer-paper': {
                boxSizing: 'border-box',
                width: drawerWidth,
                borderRight: '1px solid',
                borderColor: 'divider',
                boxShadow: 'none'
              },
            }}
            open
          >
            {drawer}
          </Drawer>
        </Box>

        {/* Main Content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: 3,
            width: { md: `calc(100% - ${drawerWidth}px)` },
            bgcolor: 'background.default',
            minHeight: '100vh',
            mt: '64px', // AppBar height
          }}
        >
          <Container maxWidth="xl">
            <Routes>
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/lead-import" element={<LeadImportPage />} />
              <Route path="/lead-management" element={<LeadManagementPage />} />
              <Route path="/form-discovery-submission" element={<FormDiscoveryAndSubmissionPage />} />
              <Route path="/audit-orchestrator" element={<LeadAuditOrchestratorPage />} />
              <Route path="/response-monitoring" element={<ResponseMonitoring />} />
              <Route path="/audit-report" element={<AuditReportBalanced />} />
              <Route path="/analytics" element={<AnalyticsPage />} />
              <Route path="/settings" element={<SettingsPage />} />
              <Route path="/test" element={<TestComponent />} />
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="*" element={<Navigate to="/dashboard" replace />} />

              {/* Legacy routes for backward compatibility */}
              <Route path="/form-discovery" element={<Navigate to="/form-discovery-submission" replace />} />
              <Route path="/bulk-submission" element={<Navigate to="/form-discovery-submission" replace />} />
            </Routes>
          </Container>
        </Box>
      </Box>
    </BrowserRouter>
  );
}

export default App;

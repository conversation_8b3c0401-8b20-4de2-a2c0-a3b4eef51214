# Lead Generation System Environment Configuration

# Supabase Configuration
SUPABASE_URL=https://hfszxryzywtpbddqsnyq.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Service URLs (for inter-service communication)
FORM_DISCOVERY_URL=http://localhost:8001
RESPONSE_MONITORING_URL=http://localhost:8002
LEAD_MANAGEMENT_URL=http://localhost:8003
ANALYTICS_URL=http://localhost:8004
AI_ORCHESTRATOR_URL=http://localhost:8006
OUTREACH_AGENT_URL=http://localhost:8007
LEAD_AUDIT_ORCHESTRATOR_URL=http://localhost:8008
API_GATEWAY_URL=http://localhost:8000

# Email Configuration (for response monitoring)
EMAIL_IMAP_SERVER=imap.gmail.com
EMAIL_IMAP_PORT=993
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password

# SMS Configuration (Telnyx)
TELNYX_API_KEY=your_telnyx_api_key
TELNYX_WEBHOOK_URL=http://localhost:8002/webhook/sms

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=leadgen
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# MongoDB Configuration
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DB=leadgen

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_TOPIC_PREFIX=leadgen

# Security Configuration
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Feature Flags
ENABLE_AI_REASONING=true
ENABLE_MOCK_DATA=false
ENABLE_AUDIT_LOGGING=true
ENABLE_RATE_LIMITING=true

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Development Configuration
DEBUG=false
ENVIRONMENT=production
